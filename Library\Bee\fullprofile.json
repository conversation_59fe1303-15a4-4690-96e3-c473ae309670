{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 15424, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 15424, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 15424, "tid": 4067, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 15424, "tid": 4067, "ts": 1751454321283016, "dur": 521, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 15424, "tid": 4067, "ts": 1751454321285979, "dur": 865, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 15424, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 15424, "tid": 1, "ts": 1751454320616494, "dur": 7267, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 15424, "tid": 1, "ts": 1751454320623779, "dur": 149741, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 15424, "tid": 1, "ts": 1751454320773538, "dur": 130319, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 15424, "tid": 4067, "ts": 1751454321286853, "dur": 31, "ph": "X", "name": "", "args": {}}, {"pid": 15424, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320614297, "dur": 7391, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320621692, "dur": 652002, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320622624, "dur": 3114, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320625749, "dur": 1724, "ph": "X", "name": "ProcessMessages 20487", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320627480, "dur": 291, "ph": "X", "name": "ReadAsync 20487", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320627779, "dur": 25, "ph": "X", "name": "ProcessMessages 20547", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320627806, "dur": 45, "ph": "X", "name": "ReadAsync 20547", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320627853, "dur": 3, "ph": "X", "name": "ProcessMessages 1988", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320627858, "dur": 21, "ph": "X", "name": "ReadAsync 1988", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320627880, "dur": 1, "ph": "X", "name": "ProcessMessages 657", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320627883, "dur": 22, "ph": "X", "name": "ReadAsync 657", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320627911, "dur": 3, "ph": "X", "name": "ProcessMessages 363", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320627917, "dur": 29, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320627948, "dur": 1, "ph": "X", "name": "ProcessMessages 56", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320627950, "dur": 9, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320627963, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320627977, "dur": 1, "ph": "X", "name": "ProcessMessages 268", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320627979, "dur": 38, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320628024, "dur": 3, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320628029, "dur": 105, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320628140, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320628144, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320628208, "dur": 4, "ph": "X", "name": "ProcessMessages 1880", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320628214, "dur": 72, "ph": "X", "name": "ReadAsync 1880", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320628293, "dur": 3, "ph": "X", "name": "ProcessMessages 881", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320628298, "dur": 30, "ph": "X", "name": "ReadAsync 881", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320628330, "dur": 2, "ph": "X", "name": "ProcessMessages 1011", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320628334, "dur": 21, "ph": "X", "name": "ReadAsync 1011", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320628361, "dur": 3, "ph": "X", "name": "ProcessMessages 324", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320628366, "dur": 34, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320628402, "dur": 2, "ph": "X", "name": "ProcessMessages 810", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320628405, "dur": 29, "ph": "X", "name": "ReadAsync 810", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320628438, "dur": 2, "ph": "X", "name": "ProcessMessages 441", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320628441, "dur": 26, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320628470, "dur": 8, "ph": "X", "name": "ProcessMessages 537", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320628480, "dur": 35, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320628521, "dur": 3, "ph": "X", "name": "ProcessMessages 286", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320628526, "dur": 29, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320628556, "dur": 2, "ph": "X", "name": "ProcessMessages 973", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320628560, "dur": 46, "ph": "X", "name": "ReadAsync 973", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320628607, "dur": 2, "ph": "X", "name": "ProcessMessages 693", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320628611, "dur": 14, "ph": "X", "name": "ReadAsync 693", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320628627, "dur": 1, "ph": "X", "name": "ProcessMessages 136", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320628629, "dur": 18, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320628649, "dur": 1, "ph": "X", "name": "ProcessMessages 624", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320628653, "dur": 37, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320628693, "dur": 1, "ph": "X", "name": "ProcessMessages 396", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320628695, "dur": 74, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320628776, "dur": 3, "ph": "X", "name": "ProcessMessages 551", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320628781, "dur": 30, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320628813, "dur": 2, "ph": "X", "name": "ProcessMessages 1307", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320628820, "dur": 19, "ph": "X", "name": "ReadAsync 1307", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320628841, "dur": 2, "ph": "X", "name": "ProcessMessages 513", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320628845, "dur": 14, "ph": "X", "name": "ReadAsync 513", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320628860, "dur": 1, "ph": "X", "name": "ProcessMessages 282", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320628863, "dur": 16, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320628881, "dur": 5, "ph": "X", "name": "ProcessMessages 245", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320628888, "dur": 21, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320628911, "dur": 8, "ph": "X", "name": "ProcessMessages 348", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320628921, "dur": 27, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320628951, "dur": 1, "ph": "X", "name": "ProcessMessages 403", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320628954, "dur": 73, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320629033, "dur": 3, "ph": "X", "name": "ProcessMessages 546", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320629038, "dur": 40, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320629081, "dur": 3, "ph": "X", "name": "ProcessMessages 1717", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320629086, "dur": 22, "ph": "X", "name": "ReadAsync 1717", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320629110, "dur": 1, "ph": "X", "name": "ProcessMessages 616", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320629113, "dur": 65, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320629184, "dur": 3, "ph": "X", "name": "ProcessMessages 258", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320629189, "dur": 38, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320629229, "dur": 3, "ph": "X", "name": "ProcessMessages 1774", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320629233, "dur": 19, "ph": "X", "name": "ReadAsync 1774", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320629255, "dur": 1, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320629257, "dur": 67, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320629330, "dur": 3, "ph": "X", "name": "ProcessMessages 473", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320629336, "dur": 31, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320629369, "dur": 2, "ph": "X", "name": "ProcessMessages 935", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320629376, "dur": 22, "ph": "X", "name": "ReadAsync 935", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320629399, "dur": 1, "ph": "X", "name": "ProcessMessages 664", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320629402, "dur": 16, "ph": "X", "name": "ReadAsync 664", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320629420, "dur": 1, "ph": "X", "name": "ProcessMessages 370", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320629423, "dur": 73, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320629503, "dur": 3, "ph": "X", "name": "ProcessMessages 581", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320629508, "dur": 40, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320629550, "dur": 2, "ph": "X", "name": "ProcessMessages 1699", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320629554, "dur": 17, "ph": "X", "name": "ReadAsync 1699", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320629574, "dur": 1, "ph": "X", "name": "ProcessMessages 428", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320629579, "dur": 22, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320629603, "dur": 1, "ph": "X", "name": "ProcessMessages 562", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320629606, "dur": 76, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320629688, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320629693, "dur": 43, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320629743, "dur": 3, "ph": "X", "name": "ProcessMessages 2172", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320629748, "dur": 18, "ph": "X", "name": "ReadAsync 2172", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320629767, "dur": 1, "ph": "X", "name": "ProcessMessages 704", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320629770, "dur": 23, "ph": "X", "name": "ReadAsync 704", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320629796, "dur": 2, "ph": "X", "name": "ProcessMessages 415", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320629800, "dur": 63, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320629869, "dur": 5, "ph": "X", "name": "ProcessMessages 773", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320629876, "dur": 53, "ph": "X", "name": "ReadAsync 773", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320629934, "dur": 4, "ph": "X", "name": "ProcessMessages 1006", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320629941, "dur": 48, "ph": "X", "name": "ReadAsync 1006", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320629995, "dur": 5, "ph": "X", "name": "ProcessMessages 900", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320630003, "dur": 33, "ph": "X", "name": "ReadAsync 900", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320630040, "dur": 2, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320630044, "dur": 28, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320630077, "dur": 2, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320630087, "dur": 56, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320630147, "dur": 3, "ph": "X", "name": "ProcessMessages 870", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320630151, "dur": 33, "ph": "X", "name": "ReadAsync 870", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320630187, "dur": 2, "ph": "X", "name": "ProcessMessages 528", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320630191, "dur": 16, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320630210, "dur": 1, "ph": "X", "name": "ProcessMessages 75", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320630214, "dur": 24, "ph": "X", "name": "ReadAsync 75", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320630242, "dur": 1, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320630245, "dur": 20, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320630268, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320630272, "dur": 23, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320630302, "dur": 2, "ph": "X", "name": "ProcessMessages 351", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320630306, "dur": 27, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320630337, "dur": 2, "ph": "X", "name": "ProcessMessages 588", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320630341, "dur": 30, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320630375, "dur": 2, "ph": "X", "name": "ProcessMessages 527", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320630379, "dur": 26, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320630408, "dur": 2, "ph": "X", "name": "ProcessMessages 354", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320630412, "dur": 18, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320630434, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320630437, "dur": 38, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320630478, "dur": 1, "ph": "X", "name": "ProcessMessages 296", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320630482, "dur": 27, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320630512, "dur": 2, "ph": "X", "name": "ProcessMessages 469", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320630515, "dur": 42, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320630561, "dur": 3, "ph": "X", "name": "ProcessMessages 715", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320630570, "dur": 27, "ph": "X", "name": "ReadAsync 715", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320630600, "dur": 2, "ph": "X", "name": "ProcessMessages 572", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320630605, "dur": 22, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320630630, "dur": 1, "ph": "X", "name": "ProcessMessages 252", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320630633, "dur": 19, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320630659, "dur": 3, "ph": "X", "name": "ProcessMessages 54", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320630664, "dur": 29, "ph": "X", "name": "ReadAsync 54", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320630697, "dur": 2, "ph": "X", "name": "ProcessMessages 692", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320630701, "dur": 31, "ph": "X", "name": "ReadAsync 692", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320630736, "dur": 2, "ph": "X", "name": "ProcessMessages 557", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320630740, "dur": 32, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320630775, "dur": 2, "ph": "X", "name": "ProcessMessages 576", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320630779, "dur": 19, "ph": "X", "name": "ReadAsync 576", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320630801, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320630805, "dur": 215, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320631028, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320631033, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320631070, "dur": 4, "ph": "X", "name": "ProcessMessages 1007", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320631076, "dur": 25, "ph": "X", "name": "ReadAsync 1007", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320631105, "dur": 2, "ph": "X", "name": "ProcessMessages 389", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320631110, "dur": 32, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320631145, "dur": 3, "ph": "X", "name": "ProcessMessages 454", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320631150, "dur": 24, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320631178, "dur": 2, "ph": "X", "name": "ProcessMessages 310", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320631182, "dur": 26, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320631211, "dur": 1, "ph": "X", "name": "ProcessMessages 310", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320631215, "dur": 22, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320631239, "dur": 1, "ph": "X", "name": "ProcessMessages 228", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320631242, "dur": 28, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320631273, "dur": 2, "ph": "X", "name": "ProcessMessages 514", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320631278, "dur": 32, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320631313, "dur": 1, "ph": "X", "name": "ProcessMessages 522", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320631316, "dur": 24, "ph": "X", "name": "ReadAsync 522", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320631344, "dur": 2, "ph": "X", "name": "ProcessMessages 373", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320631348, "dur": 23, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320631373, "dur": 1, "ph": "X", "name": "ProcessMessages 325", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320631377, "dur": 30, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320631411, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320631418, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320631449, "dur": 8, "ph": "X", "name": "ProcessMessages 691", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320631460, "dur": 39, "ph": "X", "name": "ReadAsync 691", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320631504, "dur": 2, "ph": "X", "name": "ProcessMessages 879", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320631509, "dur": 28, "ph": "X", "name": "ReadAsync 879", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320631541, "dur": 3, "ph": "X", "name": "ProcessMessages 394", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320631546, "dur": 18, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320631566, "dur": 1, "ph": "X", "name": "ProcessMessages 316", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320631570, "dur": 41, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320631613, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320631616, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320631646, "dur": 1, "ph": "X", "name": "ProcessMessages 568", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320631649, "dur": 23, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320631675, "dur": 2, "ph": "X", "name": "ProcessMessages 328", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320631679, "dur": 33, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320631716, "dur": 2, "ph": "X", "name": "ProcessMessages 497", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320631721, "dur": 27, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320631752, "dur": 2, "ph": "X", "name": "ProcessMessages 547", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320631756, "dur": 22, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320631781, "dur": 1, "ph": "X", "name": "ProcessMessages 142", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320631783, "dur": 22, "ph": "X", "name": "ReadAsync 142", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320631808, "dur": 1, "ph": "X", "name": "ProcessMessages 481", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320631812, "dur": 25, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320631840, "dur": 1, "ph": "X", "name": "ProcessMessages 338", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320631843, "dur": 24, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320631870, "dur": 2, "ph": "X", "name": "ProcessMessages 398", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320631874, "dur": 26, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320631903, "dur": 2, "ph": "X", "name": "ProcessMessages 588", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320631907, "dur": 21, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320631931, "dur": 1, "ph": "X", "name": "ProcessMessages 325", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320631934, "dur": 45, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320631982, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320631985, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320632015, "dur": 1, "ph": "X", "name": "ProcessMessages 577", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320632018, "dur": 30, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320632055, "dur": 2, "ph": "X", "name": "ProcessMessages 400", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320632059, "dur": 28, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320632090, "dur": 2, "ph": "X", "name": "ProcessMessages 551", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320632094, "dur": 26, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320632123, "dur": 1, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320632127, "dur": 18, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320632149, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320632152, "dur": 29, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320632184, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320632187, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320632215, "dur": 1, "ph": "X", "name": "ProcessMessages 556", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320632218, "dur": 22, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320632242, "dur": 1, "ph": "X", "name": "ProcessMessages 517", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320632246, "dur": 23, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320632273, "dur": 2, "ph": "X", "name": "ProcessMessages 334", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320632278, "dur": 23, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320632302, "dur": 1, "ph": "X", "name": "ProcessMessages 576", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320632306, "dur": 19, "ph": "X", "name": "ReadAsync 576", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320632328, "dur": 1, "ph": "X", "name": "ProcessMessages 54", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320632330, "dur": 20, "ph": "X", "name": "ReadAsync 54", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320632353, "dur": 2, "ph": "X", "name": "ProcessMessages 343", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320632357, "dur": 27, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320632388, "dur": 1, "ph": "X", "name": "ProcessMessages 307", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320632390, "dur": 24, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320632417, "dur": 2, "ph": "X", "name": "ProcessMessages 412", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320632421, "dur": 22, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320632447, "dur": 2, "ph": "X", "name": "ProcessMessages 378", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320632451, "dur": 27, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320632481, "dur": 1, "ph": "X", "name": "ProcessMessages 542", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320632485, "dur": 24, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320632514, "dur": 2, "ph": "X", "name": "ProcessMessages 393", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320632519, "dur": 26, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320632548, "dur": 2, "ph": "X", "name": "ProcessMessages 340", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320632553, "dur": 23, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320632578, "dur": 1, "ph": "X", "name": "ProcessMessages 340", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320632582, "dur": 28, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320632613, "dur": 3, "ph": "X", "name": "ProcessMessages 574", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320632618, "dur": 40, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320632664, "dur": 5, "ph": "X", "name": "ProcessMessages 545", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320632672, "dur": 32, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320632706, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320632715, "dur": 34, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320632752, "dur": 2, "ph": "X", "name": "ProcessMessages 655", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320632757, "dur": 27, "ph": "X", "name": "ReadAsync 655", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320632789, "dur": 2, "ph": "X", "name": "ProcessMessages 358", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320632793, "dur": 207, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320633003, "dur": 1, "ph": "X", "name": "ProcessMessages 570", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320633006, "dur": 28, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320633038, "dur": 2, "ph": "X", "name": "ProcessMessages 554", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320633042, "dur": 269, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320633317, "dur": 3, "ph": "X", "name": "ProcessMessages 653", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320633323, "dur": 118, "ph": "X", "name": "ReadAsync 653", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320633447, "dur": 4, "ph": "X", "name": "ProcessMessages 655", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320633454, "dur": 54, "ph": "X", "name": "ReadAsync 655", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320633514, "dur": 5, "ph": "X", "name": "ProcessMessages 2110", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320633521, "dur": 38, "ph": "X", "name": "ReadAsync 2110", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320633563, "dur": 2, "ph": "X", "name": "ProcessMessages 422", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320633568, "dur": 38, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320633610, "dur": 2, "ph": "X", "name": "ProcessMessages 685", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320633615, "dur": 25, "ph": "X", "name": "ReadAsync 685", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320633643, "dur": 2, "ph": "X", "name": "ProcessMessages 638", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320633648, "dur": 32, "ph": "X", "name": "ReadAsync 638", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320633683, "dur": 2, "ph": "X", "name": "ProcessMessages 590", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320633688, "dur": 25, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320633717, "dur": 2, "ph": "X", "name": "ProcessMessages 319", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320633721, "dur": 38, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320633765, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320633770, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320633812, "dur": 2, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320633816, "dur": 30, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320633850, "dur": 2, "ph": "X", "name": "ProcessMessages 379", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320633854, "dur": 36, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320633894, "dur": 3, "ph": "X", "name": "ProcessMessages 948", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320633900, "dur": 39, "ph": "X", "name": "ReadAsync 948", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320633943, "dur": 2, "ph": "X", "name": "ProcessMessages 627", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320633948, "dur": 68, "ph": "X", "name": "ReadAsync 627", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320634020, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320634024, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320634055, "dur": 2, "ph": "X", "name": "ProcessMessages 649", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320634059, "dur": 24, "ph": "X", "name": "ReadAsync 649", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320634086, "dur": 3, "ph": "X", "name": "ProcessMessages 614", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320634091, "dur": 37, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320634132, "dur": 3, "ph": "X", "name": "ProcessMessages 570", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320634137, "dur": 28, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320634168, "dur": 2, "ph": "X", "name": "ProcessMessages 307", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320634172, "dur": 36, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320634216, "dur": 4, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320634223, "dur": 38, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320634264, "dur": 2, "ph": "X", "name": "ProcessMessages 911", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320634268, "dur": 28, "ph": "X", "name": "ReadAsync 911", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320634302, "dur": 3, "ph": "X", "name": "ProcessMessages 407", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320634308, "dur": 42, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320634355, "dur": 3, "ph": "X", "name": "ProcessMessages 652", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320634361, "dur": 35, "ph": "X", "name": "ReadAsync 652", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320634399, "dur": 2, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320634403, "dur": 27, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320634432, "dur": 1, "ph": "X", "name": "ProcessMessages 624", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320634435, "dur": 25, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320634466, "dur": 3, "ph": "X", "name": "ProcessMessages 428", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320634471, "dur": 86, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320634565, "dur": 4, "ph": "X", "name": "ProcessMessages 633", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320634572, "dur": 95, "ph": "X", "name": "ReadAsync 633", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320634674, "dur": 3, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320634679, "dur": 55, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320634739, "dur": 4, "ph": "X", "name": "ProcessMessages 2362", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320634744, "dur": 29, "ph": "X", "name": "ReadAsync 2362", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320634776, "dur": 1, "ph": "X", "name": "ProcessMessages 191", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320634779, "dur": 26, "ph": "X", "name": "ReadAsync 191", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320634808, "dur": 2, "ph": "X", "name": "ProcessMessages 752", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320634811, "dur": 28, "ph": "X", "name": "ReadAsync 752", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320634842, "dur": 2, "ph": "X", "name": "ProcessMessages 559", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320634846, "dur": 26, "ph": "X", "name": "ReadAsync 559", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320634874, "dur": 1, "ph": "X", "name": "ProcessMessages 432", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320634880, "dur": 20, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320634902, "dur": 1, "ph": "X", "name": "ProcessMessages 289", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320634904, "dur": 22, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320634929, "dur": 1, "ph": "X", "name": "ProcessMessages 520", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320634931, "dur": 26, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320634961, "dur": 2, "ph": "X", "name": "ProcessMessages 596", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320634964, "dur": 28, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320634994, "dur": 2, "ph": "X", "name": "ProcessMessages 729", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320634998, "dur": 86, "ph": "X", "name": "ReadAsync 729", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320635090, "dur": 3, "ph": "X", "name": "ProcessMessages 307", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320635095, "dur": 40, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320635137, "dur": 3, "ph": "X", "name": "ProcessMessages 2332", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320635142, "dur": 19, "ph": "X", "name": "ReadAsync 2332", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320635163, "dur": 2, "ph": "X", "name": "ProcessMessages 615", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320635168, "dur": 18, "ph": "X", "name": "ReadAsync 615", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320635188, "dur": 1, "ph": "X", "name": "ProcessMessages 587", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320635191, "dur": 31, "ph": "X", "name": "ReadAsync 587", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320635226, "dur": 2, "ph": "X", "name": "ProcessMessages 527", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320635231, "dur": 27, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320635262, "dur": 2, "ph": "X", "name": "ProcessMessages 363", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320635267, "dur": 67, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320635339, "dur": 3, "ph": "X", "name": "ProcessMessages 626", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320635482, "dur": 92, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320635582, "dur": 414, "ph": "X", "name": "ProcessMessages 4737", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320636000, "dur": 102, "ph": "X", "name": "ReadAsync 4737", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320636107, "dur": 19, "ph": "X", "name": "ProcessMessages 8829", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320636133, "dur": 43, "ph": "X", "name": "ReadAsync 8829", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320636180, "dur": 3, "ph": "X", "name": "ProcessMessages 1187", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320636185, "dur": 30, "ph": "X", "name": "ReadAsync 1187", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320636219, "dur": 2, "ph": "X", "name": "ProcessMessages 716", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320636223, "dur": 28, "ph": "X", "name": "ReadAsync 716", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320636254, "dur": 2, "ph": "X", "name": "ProcessMessages 741", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320636258, "dur": 22, "ph": "X", "name": "ReadAsync 741", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320636282, "dur": 2, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320636285, "dur": 25, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320636313, "dur": 2, "ph": "X", "name": "ProcessMessages 900", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320636318, "dur": 18, "ph": "X", "name": "ReadAsync 900", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320636339, "dur": 1, "ph": "X", "name": "ProcessMessages 69", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320636343, "dur": 28, "ph": "X", "name": "ReadAsync 69", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320636374, "dur": 1, "ph": "X", "name": "ProcessMessages 280", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320636377, "dur": 23, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320636403, "dur": 2, "ph": "X", "name": "ProcessMessages 597", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320636406, "dur": 20, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320636429, "dur": 1, "ph": "X", "name": "ProcessMessages 332", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320636434, "dur": 27, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320636464, "dur": 2, "ph": "X", "name": "ProcessMessages 1026", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320636468, "dur": 153, "ph": "X", "name": "ReadAsync 1026", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320636626, "dur": 4, "ph": "X", "name": "ProcessMessages 700", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320636633, "dur": 45, "ph": "X", "name": "ReadAsync 700", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320636680, "dur": 4, "ph": "X", "name": "ProcessMessages 2566", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320636685, "dur": 25, "ph": "X", "name": "ReadAsync 2566", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320636714, "dur": 2, "ph": "X", "name": "ProcessMessages 458", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320636718, "dur": 54, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320636774, "dur": 2, "ph": "X", "name": "ProcessMessages 949", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320636777, "dur": 25, "ph": "X", "name": "ReadAsync 949", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320636804, "dur": 2, "ph": "X", "name": "ProcessMessages 965", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320636808, "dur": 48, "ph": "X", "name": "ReadAsync 965", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320636859, "dur": 2, "ph": "X", "name": "ProcessMessages 625", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320636866, "dur": 40, "ph": "X", "name": "ReadAsync 625", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320636911, "dur": 5, "ph": "X", "name": "ProcessMessages 1407", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320636919, "dur": 32, "ph": "X", "name": "ReadAsync 1407", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320636954, "dur": 2, "ph": "X", "name": "ProcessMessages 971", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320636957, "dur": 26, "ph": "X", "name": "ReadAsync 971", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320636987, "dur": 2, "ph": "X", "name": "ProcessMessages 539", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320636991, "dur": 24, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320637017, "dur": 2, "ph": "X", "name": "ProcessMessages 582", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320637021, "dur": 21, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320637045, "dur": 1, "ph": "X", "name": "ProcessMessages 392", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320637048, "dur": 20, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320637070, "dur": 1, "ph": "X", "name": "ProcessMessages 255", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320637074, "dur": 22, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320637099, "dur": 2, "ph": "X", "name": "ProcessMessages 560", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320637102, "dur": 22, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320637127, "dur": 2, "ph": "X", "name": "ProcessMessages 485", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320637130, "dur": 16, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320637148, "dur": 1, "ph": "X", "name": "ProcessMessages 280", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320637152, "dur": 19, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320637174, "dur": 1, "ph": "X", "name": "ProcessMessages 422", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320637177, "dur": 27, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320637206, "dur": 2, "ph": "X", "name": "ProcessMessages 709", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320637210, "dur": 23, "ph": "X", "name": "ReadAsync 709", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320637235, "dur": 1, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320637238, "dur": 21, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320637262, "dur": 1, "ph": "X", "name": "ProcessMessages 476", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320637265, "dur": 26, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320637294, "dur": 1, "ph": "X", "name": "ProcessMessages 429", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320637299, "dur": 27, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320637329, "dur": 2, "ph": "X", "name": "ProcessMessages 798", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320637332, "dur": 83, "ph": "X", "name": "ReadAsync 798", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320637417, "dur": 2, "ph": "X", "name": "ProcessMessages 391", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320637422, "dur": 48, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320637473, "dur": 3, "ph": "X", "name": "ProcessMessages 1749", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320637478, "dur": 20, "ph": "X", "name": "ReadAsync 1749", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320637500, "dur": 1, "ph": "X", "name": "ProcessMessages 307", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320637502, "dur": 25, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320637534, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320637559, "dur": 1, "ph": "X", "name": "ProcessMessages 649", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320637563, "dur": 22, "ph": "X", "name": "ReadAsync 649", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320637590, "dur": 2, "ph": "X", "name": "ProcessMessages 337", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320637595, "dur": 45, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320637646, "dur": 4, "ph": "X", "name": "ProcessMessages 682", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320637653, "dur": 35, "ph": "X", "name": "ReadAsync 682", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320637691, "dur": 2, "ph": "X", "name": "ProcessMessages 763", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320637695, "dur": 32, "ph": "X", "name": "ReadAsync 763", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320637731, "dur": 3, "ph": "X", "name": "ProcessMessages 734", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320637736, "dur": 32, "ph": "X", "name": "ReadAsync 734", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320637770, "dur": 2, "ph": "X", "name": "ProcessMessages 813", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320637774, "dur": 25, "ph": "X", "name": "ReadAsync 813", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320637802, "dur": 2, "ph": "X", "name": "ProcessMessages 374", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320637807, "dur": 31, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320637845, "dur": 3, "ph": "X", "name": "ProcessMessages 885", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320637850, "dur": 20, "ph": "X", "name": "ReadAsync 885", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320637872, "dur": 1, "ph": "X", "name": "ProcessMessages 354", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320637875, "dur": 26, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320637905, "dur": 2, "ph": "X", "name": "ProcessMessages 397", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320637909, "dur": 22, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320637933, "dur": 2, "ph": "X", "name": "ProcessMessages 456", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320637938, "dur": 19, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320637960, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320637962, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320637995, "dur": 2, "ph": "X", "name": "ProcessMessages 566", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320637999, "dur": 25, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320638026, "dur": 2, "ph": "X", "name": "ProcessMessages 628", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320638030, "dur": 31, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320638066, "dur": 3, "ph": "X", "name": "ProcessMessages 289", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320638071, "dur": 47, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320638124, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320638127, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320638156, "dur": 2, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320638160, "dur": 23, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320638186, "dur": 2, "ph": "X", "name": "ProcessMessages 685", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320638190, "dur": 101, "ph": "X", "name": "ReadAsync 685", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320638297, "dur": 4, "ph": "X", "name": "ProcessMessages 678", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320638304, "dur": 105, "ph": "X", "name": "ReadAsync 678", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320638413, "dur": 6, "ph": "X", "name": "ProcessMessages 2761", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320638422, "dur": 94, "ph": "X", "name": "ReadAsync 2761", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320638521, "dur": 4, "ph": "X", "name": "ProcessMessages 1881", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320638528, "dur": 92, "ph": "X", "name": "ReadAsync 1881", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320638625, "dur": 5, "ph": "X", "name": "ProcessMessages 2170", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320638634, "dur": 99, "ph": "X", "name": "ReadAsync 2170", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320638737, "dur": 5, "ph": "X", "name": "ProcessMessages 2156", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320638745, "dur": 92, "ph": "X", "name": "ReadAsync 2156", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320638841, "dur": 5, "ph": "X", "name": "ProcessMessages 1836", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320638849, "dur": 89, "ph": "X", "name": "ReadAsync 1836", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320638942, "dur": 5, "ph": "X", "name": "ProcessMessages 1845", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320638949, "dur": 91, "ph": "X", "name": "ReadAsync 1845", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320639044, "dur": 5, "ph": "X", "name": "ProcessMessages 2092", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320639052, "dur": 89, "ph": "X", "name": "ReadAsync 2092", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320639144, "dur": 5, "ph": "X", "name": "ProcessMessages 2410", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320639151, "dur": 97, "ph": "X", "name": "ReadAsync 2410", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320639252, "dur": 4, "ph": "X", "name": "ProcessMessages 1914", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320639259, "dur": 94, "ph": "X", "name": "ReadAsync 1914", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320639358, "dur": 5, "ph": "X", "name": "ProcessMessages 1795", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320639365, "dur": 92, "ph": "X", "name": "ReadAsync 1795", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320639461, "dur": 6, "ph": "X", "name": "ProcessMessages 1691", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320639470, "dur": 95, "ph": "X", "name": "ReadAsync 1691", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320639569, "dur": 6, "ph": "X", "name": "ProcessMessages 2012", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320639577, "dur": 45, "ph": "X", "name": "ReadAsync 2012", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320639625, "dur": 4, "ph": "X", "name": "ProcessMessages 1530", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320639632, "dur": 22, "ph": "X", "name": "ReadAsync 1530", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320639656, "dur": 2, "ph": "X", "name": "ProcessMessages 668", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320639661, "dur": 34, "ph": "X", "name": "ReadAsync 668", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320639703, "dur": 4, "ph": "X", "name": "ProcessMessages 560", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320639710, "dur": 39, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320639752, "dur": 3, "ph": "X", "name": "ProcessMessages 827", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320639757, "dur": 17, "ph": "X", "name": "ReadAsync 827", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320639776, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320639780, "dur": 37, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320639821, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320639824, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320639849, "dur": 2, "ph": "X", "name": "ProcessMessages 533", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320639854, "dur": 20, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320639877, "dur": 1, "ph": "X", "name": "ProcessMessages 322", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320639880, "dur": 27, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320639911, "dur": 2, "ph": "X", "name": "ProcessMessages 136", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320639915, "dur": 53, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320639972, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320639975, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320640003, "dur": 2, "ph": "X", "name": "ProcessMessages 573", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320640007, "dur": 20, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320640029, "dur": 2, "ph": "X", "name": "ProcessMessages 570", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320640035, "dur": 29, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320640068, "dur": 3, "ph": "X", "name": "ProcessMessages 630", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320640072, "dur": 25, "ph": "X", "name": "ReadAsync 630", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320640099, "dur": 2, "ph": "X", "name": "ProcessMessages 734", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320640104, "dur": 13, "ph": "X", "name": "ReadAsync 734", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320640118, "dur": 1, "ph": "X", "name": "ProcessMessages 71", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320640122, "dur": 17, "ph": "X", "name": "ReadAsync 71", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320640141, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320640145, "dur": 61, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320640210, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320640213, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320640240, "dur": 2, "ph": "X", "name": "ProcessMessages 707", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320640245, "dur": 18, "ph": "X", "name": "ReadAsync 707", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320640265, "dur": 2, "ph": "X", "name": "ProcessMessages 473", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320640269, "dur": 26, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320640299, "dur": 2, "ph": "X", "name": "ProcessMessages 596", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320640303, "dur": 28, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320640334, "dur": 2, "ph": "X", "name": "ProcessMessages 685", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320640338, "dur": 15, "ph": "X", "name": "ReadAsync 685", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320640355, "dur": 1, "ph": "X", "name": "ProcessMessages 168", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320640359, "dur": 20, "ph": "X", "name": "ReadAsync 168", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320640383, "dur": 2, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320640387, "dur": 47, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320640437, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320640440, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320640467, "dur": 2, "ph": "X", "name": "ProcessMessages 725", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320640471, "dur": 22, "ph": "X", "name": "ReadAsync 725", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320640496, "dur": 2, "ph": "X", "name": "ProcessMessages 370", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320640506, "dur": 50, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320640559, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320640562, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320640587, "dur": 2, "ph": "X", "name": "ProcessMessages 604", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320640592, "dur": 23, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320640618, "dur": 2, "ph": "X", "name": "ProcessMessages 468", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320640623, "dur": 52, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320640678, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320640681, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320640712, "dur": 2, "ph": "X", "name": "ProcessMessages 581", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320640717, "dur": 28, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320640748, "dur": 2, "ph": "X", "name": "ProcessMessages 455", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320640752, "dur": 14, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320640769, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320640772, "dur": 256, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320641033, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320641036, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320641100, "dur": 7, "ph": "X", "name": "ProcessMessages 3022", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320641110, "dur": 18, "ph": "X", "name": "ReadAsync 3022", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320641131, "dur": 1, "ph": "X", "name": "ProcessMessages 139", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320641134, "dur": 44, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320641182, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320641185, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320641216, "dur": 2, "ph": "X", "name": "ProcessMessages 540", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320641220, "dur": 19, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320641244, "dur": 2, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320641248, "dur": 76, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320641328, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320641331, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320641358, "dur": 2, "ph": "X", "name": "ProcessMessages 738", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320641362, "dur": 25, "ph": "X", "name": "ReadAsync 738", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320641392, "dur": 2, "ph": "X", "name": "ProcessMessages 376", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320641397, "dur": 57, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320641457, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320641460, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320641486, "dur": 2, "ph": "X", "name": "ProcessMessages 701", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320641490, "dur": 17, "ph": "X", "name": "ReadAsync 701", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320641509, "dur": 2, "ph": "X", "name": "ProcessMessages 361", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320641513, "dur": 61, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320641578, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320641581, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320641607, "dur": 2, "ph": "X", "name": "ProcessMessages 609", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320641611, "dur": 21, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320641634, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320641640, "dur": 15, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320641657, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320641660, "dur": 45, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320641709, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320641715, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320641749, "dur": 2, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320641753, "dur": 25, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320641784, "dur": 2, "ph": "X", "name": "ProcessMessages 609", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320641788, "dur": 43, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320641835, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320641838, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320641866, "dur": 2, "ph": "X", "name": "ProcessMessages 714", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320641871, "dur": 17, "ph": "X", "name": "ReadAsync 714", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320641890, "dur": 4, "ph": "X", "name": "ProcessMessages 361", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320641897, "dur": 53, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320641954, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320641957, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320641984, "dur": 2, "ph": "X", "name": "ProcessMessages 726", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320641988, "dur": 23, "ph": "X", "name": "ReadAsync 726", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320642015, "dur": 2, "ph": "X", "name": "ProcessMessages 376", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320642020, "dur": 53, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320642077, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320642080, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320642109, "dur": 5, "ph": "X", "name": "ProcessMessages 807", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320642116, "dur": 24, "ph": "X", "name": "ReadAsync 807", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320642144, "dur": 2, "ph": "X", "name": "ProcessMessages 382", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320642148, "dur": 97, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320642249, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320642252, "dur": 84, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320642347, "dur": 4, "ph": "X", "name": "ProcessMessages 1297", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320642353, "dur": 38, "ph": "X", "name": "ReadAsync 1297", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320642394, "dur": 2, "ph": "X", "name": "ProcessMessages 1267", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320642398, "dur": 23, "ph": "X", "name": "ReadAsync 1267", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320642424, "dur": 1, "ph": "X", "name": "ProcessMessages 287", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320642427, "dur": 24, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320642453, "dur": 1, "ph": "X", "name": "ProcessMessages 786", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320642456, "dur": 21, "ph": "X", "name": "ReadAsync 786", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320642479, "dur": 1, "ph": "X", "name": "ProcessMessages 139", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320642482, "dur": 88, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320642574, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320642577, "dur": 89, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320642672, "dur": 4, "ph": "X", "name": "ProcessMessages 1102", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320642678, "dur": 42, "ph": "X", "name": "ReadAsync 1102", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320642722, "dur": 2, "ph": "X", "name": "ProcessMessages 1098", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320642726, "dur": 20, "ph": "X", "name": "ReadAsync 1098", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320642749, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320642751, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320642778, "dur": 1, "ph": "X", "name": "ProcessMessages 583", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320642781, "dur": 17, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320642801, "dur": 2, "ph": "X", "name": "ProcessMessages 461", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320642805, "dur": 94, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320642910, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320642913, "dur": 94, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320643011, "dur": 4, "ph": "X", "name": "ProcessMessages 996", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320643018, "dur": 52, "ph": "X", "name": "ReadAsync 996", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320643074, "dur": 6, "ph": "X", "name": "ProcessMessages 1805", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320643082, "dur": 14, "ph": "X", "name": "ReadAsync 1805", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320643098, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320643100, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320643126, "dur": 3, "ph": "X", "name": "ProcessMessages 450", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320643132, "dur": 16, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320643152, "dur": 2, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320643155, "dur": 18, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320643176, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320643179, "dur": 83, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320643269, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320643274, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320643323, "dur": 3, "ph": "X", "name": "ProcessMessages 714", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320643328, "dur": 21, "ph": "X", "name": "ReadAsync 714", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320643350, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320643353, "dur": 86, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320643443, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320643446, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320643501, "dur": 5, "ph": "X", "name": "ProcessMessages 1963", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320643508, "dur": 109, "ph": "X", "name": "ReadAsync 1963", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320643626, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320643630, "dur": 70, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320643708, "dur": 5, "ph": "X", "name": "ProcessMessages 669", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320643716, "dur": 61, "ph": "X", "name": "ReadAsync 669", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320643783, "dur": 4, "ph": "X", "name": "ProcessMessages 1065", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320643789, "dur": 30, "ph": "X", "name": "ReadAsync 1065", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320643823, "dur": 2, "ph": "X", "name": "ProcessMessages 242", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320643827, "dur": 100, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320643935, "dur": 5, "ph": "X", "name": "ProcessMessages 837", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320643945, "dur": 49, "ph": "X", "name": "ReadAsync 837", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320643998, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320644002, "dur": 33, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320644040, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320644043, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320644074, "dur": 2, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320644078, "dur": 64, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320644149, "dur": 3, "ph": "X", "name": "ProcessMessages 623", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320644154, "dur": 48, "ph": "X", "name": "ReadAsync 623", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320644205, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320644211, "dur": 119, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320644337, "dur": 3, "ph": "X", "name": "ProcessMessages 645", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320644342, "dur": 32, "ph": "X", "name": "ReadAsync 645", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320644377, "dur": 2, "ph": "X", "name": "ProcessMessages 872", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320644381, "dur": 91, "ph": "X", "name": "ReadAsync 872", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320644479, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320644484, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320644547, "dur": 8, "ph": "X", "name": "ProcessMessages 2532", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320644558, "dur": 70, "ph": "X", "name": "ReadAsync 2532", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320644634, "dur": 3, "ph": "X", "name": "ProcessMessages 93", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320644639, "dur": 27, "ph": "X", "name": "ReadAsync 93", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320644670, "dur": 1, "ph": "X", "name": "ProcessMessages 222", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320644673, "dur": 23, "ph": "X", "name": "ReadAsync 222", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320644701, "dur": 2, "ph": "X", "name": "ProcessMessages 242", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320644705, "dur": 115, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320644827, "dur": 3, "ph": "X", "name": "ProcessMessages 454", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320644832, "dur": 34, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320644868, "dur": 2, "ph": "X", "name": "ProcessMessages 355", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320644871, "dur": 23, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320644897, "dur": 1, "ph": "X", "name": "ProcessMessages 167", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320644899, "dur": 85, "ph": "X", "name": "ReadAsync 167", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320644991, "dur": 3, "ph": "X", "name": "ProcessMessages 634", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320644996, "dur": 39, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320645038, "dur": 5, "ph": "X", "name": "ProcessMessages 2202", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320645046, "dur": 23, "ph": "X", "name": "ReadAsync 2202", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320645073, "dur": 2, "ph": "X", "name": "ProcessMessages 53", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320645077, "dur": 53, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320645133, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320645136, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320645167, "dur": 2, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320645171, "dur": 25, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320645202, "dur": 3, "ph": "X", "name": "ProcessMessages 366", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320645207, "dur": 21, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320645230, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320645234, "dur": 58, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320645295, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320645299, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320645330, "dur": 2, "ph": "X", "name": "ProcessMessages 860", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320645334, "dur": 18, "ph": "X", "name": "ReadAsync 860", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320645354, "dur": 2, "ph": "X", "name": "ProcessMessages 435", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320645358, "dur": 26, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320645387, "dur": 2, "ph": "X", "name": "ProcessMessages 594", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320645391, "dur": 20, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320645416, "dur": 1, "ph": "X", "name": "ProcessMessages 537", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320645419, "dur": 28, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320645452, "dur": 3, "ph": "X", "name": "ProcessMessages 372", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320645457, "dur": 17, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320645476, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320645480, "dur": 70, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320645557, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320645561, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320645587, "dur": 2, "ph": "X", "name": "ProcessMessages 520", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320645591, "dur": 25, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320645630, "dur": 2, "ph": "X", "name": "ProcessMessages 496", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320645635, "dur": 88, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320645727, "dur": 2, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320645731, "dur": 83, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320645818, "dur": 3, "ph": "X", "name": "ProcessMessages 1120", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320645822, "dur": 87, "ph": "X", "name": "ReadAsync 1120", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320645915, "dur": 3, "ph": "X", "name": "ProcessMessages 895", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320645921, "dur": 92, "ph": "X", "name": "ReadAsync 895", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320646020, "dur": 2, "ph": "X", "name": "ProcessMessages 156", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320646114, "dur": 74, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320646192, "dur": 5, "ph": "X", "name": "ProcessMessages 3158", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320646199, "dur": 93, "ph": "X", "name": "ReadAsync 3158", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320646299, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320646304, "dur": 84, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320646395, "dur": 4, "ph": "X", "name": "ProcessMessages 1120", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320646401, "dur": 42, "ph": "X", "name": "ReadAsync 1120", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320646446, "dur": 2, "ph": "X", "name": "ProcessMessages 779", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320646450, "dur": 27, "ph": "X", "name": "ReadAsync 779", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320646481, "dur": 2, "ph": "X", "name": "ProcessMessages 301", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320646484, "dur": 113, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320646601, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320646605, "dur": 84, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320646695, "dur": 4, "ph": "X", "name": "ProcessMessages 1110", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320646701, "dur": 39, "ph": "X", "name": "ReadAsync 1110", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320646744, "dur": 1, "ph": "X", "name": "ProcessMessages 185", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320646746, "dur": 27, "ph": "X", "name": "ReadAsync 185", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320646776, "dur": 2, "ph": "X", "name": "ProcessMessages 679", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320646779, "dur": 22, "ph": "X", "name": "ReadAsync 679", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320646804, "dur": 1, "ph": "X", "name": "ProcessMessages 258", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320646807, "dur": 123, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320646936, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320646940, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320646978, "dur": 4, "ph": "X", "name": "ProcessMessages 1341", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320646985, "dur": 19, "ph": "X", "name": "ReadAsync 1341", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320647006, "dur": 1, "ph": "X", "name": "ProcessMessages 336", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320647009, "dur": 26, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320647039, "dur": 2, "ph": "X", "name": "ProcessMessages 337", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320647043, "dur": 24, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320647069, "dur": 1, "ph": "X", "name": "ProcessMessages 313", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320647072, "dur": 19, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320647095, "dur": 17, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320647114, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320647117, "dur": 127, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320647248, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320647252, "dur": 98, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320647355, "dur": 4, "ph": "X", "name": "ProcessMessages 1030", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320647361, "dur": 35, "ph": "X", "name": "ReadAsync 1030", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320647400, "dur": 3, "ph": "X", "name": "ProcessMessages 490", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320647405, "dur": 34, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320647442, "dur": 2, "ph": "X", "name": "ProcessMessages 617", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320647446, "dur": 80, "ph": "X", "name": "ReadAsync 617", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320647529, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320647532, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320647570, "dur": 3, "ph": "X", "name": "ProcessMessages 531", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320647576, "dur": 43, "ph": "X", "name": "ReadAsync 531", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320647622, "dur": 3, "ph": "X", "name": "ProcessMessages 811", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320647628, "dur": 24, "ph": "X", "name": "ReadAsync 811", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320647655, "dur": 2, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320647659, "dur": 71, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320647734, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320647737, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320647764, "dur": 2, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320647768, "dur": 28, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320647800, "dur": 2, "ph": "X", "name": "ProcessMessages 399", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320647804, "dur": 118, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320647925, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320647929, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320647958, "dur": 2, "ph": "X", "name": "ProcessMessages 228", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320647962, "dur": 86, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320648052, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320648055, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320648086, "dur": 2, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320648090, "dur": 28, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320648122, "dur": 2, "ph": "X", "name": "ProcessMessages 651", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320648127, "dur": 91, "ph": "X", "name": "ReadAsync 651", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320648221, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320648224, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320648260, "dur": 2, "ph": "X", "name": "ProcessMessages 541", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320648265, "dur": 39, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320648307, "dur": 2, "ph": "X", "name": "ProcessMessages 632", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320648312, "dur": 103, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320648418, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320648428, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320648466, "dur": 3, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320648472, "dur": 31, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320648505, "dur": 2, "ph": "X", "name": "ProcessMessages 615", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320648509, "dur": 105, "ph": "X", "name": "ReadAsync 615", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320648620, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320648624, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320648656, "dur": 2, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320648660, "dur": 54, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320648718, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320648722, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320648760, "dur": 2, "ph": "X", "name": "ProcessMessages 657", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320648765, "dur": 28, "ph": "X", "name": "ReadAsync 657", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320648795, "dur": 2, "ph": "X", "name": "ProcessMessages 560", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320648800, "dur": 40, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320648844, "dur": 2, "ph": "X", "name": "ProcessMessages 504", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320648848, "dur": 30, "ph": "X", "name": "ReadAsync 504", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320648885, "dur": 3, "ph": "X", "name": "ProcessMessages 301", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320648891, "dur": 41, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320648938, "dur": 3, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320648944, "dur": 55, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320649003, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320649007, "dur": 75, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320649088, "dur": 3, "ph": "X", "name": "ProcessMessages 841", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320649094, "dur": 21, "ph": "X", "name": "ReadAsync 841", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320649117, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320649119, "dur": 23, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320649145, "dur": 2, "ph": "X", "name": "ProcessMessages 553", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320649148, "dur": 18, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320649168, "dur": 1, "ph": "X", "name": "ProcessMessages 427", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320649171, "dur": 33, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320649207, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320649209, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320649245, "dur": 1, "ph": "X", "name": "ProcessMessages 222", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320649248, "dur": 80, "ph": "X", "name": "ReadAsync 222", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320649332, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320649335, "dur": 100, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320649442, "dur": 4, "ph": "X", "name": "ProcessMessages 1003", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320649448, "dur": 20, "ph": "X", "name": "ReadAsync 1003", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320649475, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320649478, "dur": 20, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320649500, "dur": 2, "ph": "X", "name": "ProcessMessages 580", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320649504, "dur": 26, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320649534, "dur": 2, "ph": "X", "name": "ProcessMessages 650", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320649538, "dur": 59, "ph": "X", "name": "ReadAsync 650", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320649602, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320649627, "dur": 1, "ph": "X", "name": "ProcessMessages 583", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320649629, "dur": 22, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320649655, "dur": 2, "ph": "X", "name": "ProcessMessages 608", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320649658, "dur": 16, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320649675, "dur": 1, "ph": "X", "name": "ProcessMessages 67", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320649678, "dur": 61, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320649743, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320649747, "dur": 636, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320650391, "dur": 5, "ph": "X", "name": "ProcessMessages 538", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320650398, "dur": 103, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320650505, "dur": 7, "ph": "X", "name": "ProcessMessages 5252", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320650514, "dur": 110, "ph": "X", "name": "ReadAsync 5252", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320650632, "dur": 5, "ph": "X", "name": "ProcessMessages 1264", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320650639, "dur": 35, "ph": "X", "name": "ReadAsync 1264", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320650677, "dur": 2, "ph": "X", "name": "ProcessMessages 968", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320650680, "dur": 25, "ph": "X", "name": "ReadAsync 968", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320650712, "dur": 2, "ph": "X", "name": "ProcessMessages 248", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320650717, "dur": 49, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320650770, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320650773, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320650797, "dur": 2, "ph": "X", "name": "ProcessMessages 674", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320650801, "dur": 16, "ph": "X", "name": "ReadAsync 674", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320650819, "dur": 1, "ph": "X", "name": "ProcessMessages 285", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320650822, "dur": 18, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320650843, "dur": 1, "ph": "X", "name": "ProcessMessages 388", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320650846, "dur": 23, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320650872, "dur": 1, "ph": "X", "name": "ProcessMessages 171", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320650875, "dur": 19, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320650896, "dur": 2, "ph": "X", "name": "ProcessMessages 532", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320650899, "dur": 15, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320650916, "dur": 1, "ph": "X", "name": "ProcessMessages 253", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320650918, "dur": 17, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320650939, "dur": 59, "ph": "X", "name": "ReadAsync 177", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320651005, "dur": 3, "ph": "X", "name": "ProcessMessages 63", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320651009, "dur": 29, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320651040, "dur": 1, "ph": "X", "name": "ProcessMessages 746", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320651043, "dur": 15, "ph": "X", "name": "ReadAsync 746", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320651060, "dur": 1, "ph": "X", "name": "ProcessMessages 310", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320651063, "dur": 55, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320651121, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320651142, "dur": 1, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320651145, "dur": 17, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320651164, "dur": 1, "ph": "X", "name": "ProcessMessages 633", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320651166, "dur": 8, "ph": "X", "name": "ReadAsync 633", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320651178, "dur": 55, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320651237, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320651304, "dur": 3, "ph": "X", "name": "ProcessMessages 520", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320651309, "dur": 24, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320651335, "dur": 1, "ph": "X", "name": "ProcessMessages 605", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320651338, "dur": 25, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320651367, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320651384, "dur": 1, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320651387, "dur": 18, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320651407, "dur": 1, "ph": "X", "name": "ProcessMessages 574", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320651409, "dur": 15, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320651427, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320651429, "dur": 47, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320651481, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320651501, "dur": 1, "ph": "X", "name": "ProcessMessages 481", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320651508, "dur": 18, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320651528, "dur": 1, "ph": "X", "name": "ProcessMessages 700", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320651531, "dur": 16, "ph": "X", "name": "ReadAsync 700", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320651550, "dur": 1, "ph": "X", "name": "ProcessMessages 367", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320651552, "dur": 70, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320651629, "dur": 3, "ph": "X", "name": "ProcessMessages 384", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320651634, "dur": 27, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320651663, "dur": 1, "ph": "X", "name": "ProcessMessages 782", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320651666, "dur": 18, "ph": "X", "name": "ReadAsync 782", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320651687, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320651708, "dur": 1, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320651711, "dur": 19, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320651732, "dur": 1, "ph": "X", "name": "ProcessMessages 592", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320651734, "dur": 9, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320651747, "dur": 50, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320651801, "dur": 64, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320651875, "dur": 3, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320651880, "dur": 25, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320651907, "dur": 1, "ph": "X", "name": "ProcessMessages 590", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320651910, "dur": 18, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320651930, "dur": 1, "ph": "X", "name": "ProcessMessages 434", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320651933, "dur": 17, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320651952, "dur": 1, "ph": "X", "name": "ProcessMessages 511", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320651955, "dur": 16, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320651973, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320651975, "dur": 49, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320652028, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320652049, "dur": 1, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320652052, "dur": 21, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320652075, "dur": 1, "ph": "X", "name": "ProcessMessages 569", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320652078, "dur": 16, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320652097, "dur": 1, "ph": "X", "name": "ProcessMessages 58", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320652099, "dur": 86, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320652192, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320652196, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320652228, "dur": 2, "ph": "X", "name": "ProcessMessages 1095", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320652232, "dur": 16, "ph": "X", "name": "ReadAsync 1095", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320652252, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320652273, "dur": 1, "ph": "X", "name": "ProcessMessages 454", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320652275, "dur": 18, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320652295, "dur": 1, "ph": "X", "name": "ProcessMessages 591", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320652297, "dur": 50, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320652352, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320652376, "dur": 1, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320652379, "dur": 15, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320652396, "dur": 1, "ph": "X", "name": "ProcessMessages 560", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320652399, "dur": 13, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320652414, "dur": 1, "ph": "X", "name": "ProcessMessages 58", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320652417, "dur": 49, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320652470, "dur": 63, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320652539, "dur": 3, "ph": "X", "name": "ProcessMessages 523", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320652544, "dur": 24, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320652570, "dur": 1, "ph": "X", "name": "ProcessMessages 647", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320652572, "dur": 18, "ph": "X", "name": "ReadAsync 647", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320652592, "dur": 1, "ph": "X", "name": "ProcessMessages 328", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320652595, "dur": 15, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320652611, "dur": 1, "ph": "X", "name": "ProcessMessages 177", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320652613, "dur": 18, "ph": "X", "name": "ReadAsync 177", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320652633, "dur": 1, "ph": "X", "name": "ProcessMessages 456", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320652636, "dur": 21, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320652658, "dur": 1, "ph": "X", "name": "ProcessMessages 513", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320652661, "dur": 18, "ph": "X", "name": "ReadAsync 513", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320652682, "dur": 1, "ph": "X", "name": "ProcessMessages 436", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320652685, "dur": 22, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320652713, "dur": 3, "ph": "X", "name": "ProcessMessages 331", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320652718, "dur": 26, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320652745, "dur": 1, "ph": "X", "name": "ProcessMessages 494", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320652748, "dur": 13, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320652764, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320652766, "dur": 44, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320652817, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320652838, "dur": 1, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320652841, "dur": 18, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320652861, "dur": 1, "ph": "X", "name": "ProcessMessages 554", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320652864, "dur": 15, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320652883, "dur": 1, "ph": "X", "name": "ProcessMessages 67", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320652885, "dur": 44, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320652933, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320652955, "dur": 1, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320652958, "dur": 18, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320652978, "dur": 1, "ph": "X", "name": "ProcessMessages 482", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320652980, "dur": 22, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320653004, "dur": 1, "ph": "X", "name": "ProcessMessages 567", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320653007, "dur": 15, "ph": "X", "name": "ReadAsync 567", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320653023, "dur": 1, "ph": "X", "name": "ProcessMessages 481", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320653026, "dur": 15, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320653043, "dur": 1, "ph": "X", "name": "ProcessMessages 291", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320653045, "dur": 17, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320653064, "dur": 1, "ph": "X", "name": "ProcessMessages 360", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320653067, "dur": 23, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320653093, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320653095, "dur": 52, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320653152, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320653175, "dur": 1, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320653178, "dur": 23, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320653203, "dur": 1, "ph": "X", "name": "ProcessMessages 641", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320653205, "dur": 15, "ph": "X", "name": "ReadAsync 641", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320653222, "dur": 1, "ph": "X", "name": "ProcessMessages 411", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320653224, "dur": 70, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320653297, "dur": 2, "ph": "X", "name": "ProcessMessages 920", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320653300, "dur": 20, "ph": "X", "name": "ReadAsync 920", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320653324, "dur": 2, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320653327, "dur": 36, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320653366, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320653368, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320653393, "dur": 2, "ph": "X", "name": "ProcessMessages 439", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320653396, "dur": 24, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320653422, "dur": 1, "ph": "X", "name": "ProcessMessages 635", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320653425, "dur": 24, "ph": "X", "name": "ReadAsync 635", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320653452, "dur": 2, "ph": "X", "name": "ProcessMessages 626", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320653455, "dur": 24, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320653483, "dur": 2, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320653487, "dur": 18, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320653507, "dur": 1, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320653510, "dur": 15, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320653528, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320653530, "dur": 53, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320653587, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320653607, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320653609, "dur": 223, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320653838, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320653841, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320653883, "dur": 537, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320654426, "dur": 74, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320654505, "dur": 24, "ph": "X", "name": "ProcessMessages 1628", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320654532, "dur": 30, "ph": "X", "name": "ReadAsync 1628", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320654566, "dur": 5, "ph": "X", "name": "ProcessMessages 244", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320654573, "dur": 33, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320654612, "dur": 5, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320654620, "dur": 31, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320654657, "dur": 3, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320654662, "dur": 24, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320654690, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320654694, "dur": 28, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320654726, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320654730, "dur": 28, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320654763, "dur": 3, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320654769, "dur": 27, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320654799, "dur": 2, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320654803, "dur": 22, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320654830, "dur": 3, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320654835, "dur": 26, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320654865, "dur": 3, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320654871, "dur": 24, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320654898, "dur": 3, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320654904, "dur": 14, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320654921, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320654925, "dur": 18, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320654946, "dur": 2, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320654950, "dur": 29, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320654984, "dur": 4, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320654991, "dur": 38, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320655034, "dur": 4, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320655041, "dur": 26, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320655072, "dur": 3, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320655077, "dur": 28, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320655110, "dur": 3, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320655116, "dur": 23, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320655143, "dur": 3, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320655149, "dur": 22, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320655176, "dur": 3, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320655181, "dur": 88, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320655275, "dur": 6, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320655284, "dur": 34, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320655331, "dur": 5, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320655339, "dur": 35, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320655380, "dur": 4, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320655387, "dur": 28, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320655419, "dur": 4, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320655425, "dur": 33, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320655464, "dur": 4, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320655470, "dur": 32, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320655508, "dur": 3, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320655515, "dur": 33, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320655553, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320655558, "dur": 25, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320655586, "dur": 4, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320655593, "dur": 25, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320655623, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320655628, "dur": 28, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320655661, "dur": 4, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320655668, "dur": 25, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320655697, "dur": 3, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320655702, "dur": 26, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320655732, "dur": 4, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320655739, "dur": 25, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320655769, "dur": 4, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320655775, "dur": 26, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320655805, "dur": 3, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320655812, "dur": 29, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320655847, "dur": 4, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320655853, "dur": 42, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320655900, "dur": 5, "ph": "X", "name": "ProcessMessages 188", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320655908, "dur": 32, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320655945, "dur": 5, "ph": "X", "name": "ProcessMessages 180", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320655952, "dur": 35, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320655992, "dur": 3, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320655997, "dur": 24, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320656024, "dur": 4, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320656031, "dur": 28, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320656067, "dur": 4, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320656076, "dur": 29, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320656110, "dur": 4, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320656117, "dur": 65, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320656187, "dur": 5, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320656195, "dur": 34, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320656234, "dur": 4, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320656241, "dur": 29, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320656273, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320656280, "dur": 67, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320656351, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320656355, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320656391, "dur": 4, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320656397, "dur": 36, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320656438, "dur": 4, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320656444, "dur": 31, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320656479, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320656483, "dur": 31, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320656517, "dur": 2, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320656520, "dur": 24, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320656550, "dur": 3, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320656556, "dur": 33, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320656593, "dur": 3, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320656598, "dur": 31, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320656635, "dur": 3, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320656641, "dur": 30, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320656675, "dur": 3, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320656681, "dur": 36, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320656723, "dur": 4, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320656729, "dur": 25, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320656758, "dur": 3, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320656764, "dur": 24, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320656792, "dur": 3, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320656798, "dur": 29, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320656830, "dur": 3, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320656836, "dur": 26, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320656868, "dur": 4, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320656875, "dur": 31, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320656911, "dur": 4, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320656918, "dur": 37, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320656960, "dur": 5, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320656968, "dur": 34, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320657007, "dur": 4, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320657013, "dur": 32, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320657049, "dur": 4, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320657055, "dur": 28, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320657087, "dur": 4, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320657093, "dur": 35, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320657134, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320657138, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320657159, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320657163, "dur": 61, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320657230, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320657234, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320657262, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320657266, "dur": 7543, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320664823, "dur": 6, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320664833, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320664877, "dur": 4, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320664883, "dur": 163, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320665054, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320665060, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320665103, "dur": 4, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320665110, "dur": 1367, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320666485, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320666491, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320666530, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320666534, "dur": 108, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320666647, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320666651, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320666696, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320666701, "dur": 381, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320667087, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320667091, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320667142, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320667148, "dur": 217, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320667371, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320667376, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320667406, "dur": 2, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320667411, "dur": 95, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320667514, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320667521, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320667572, "dur": 3, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320667577, "dur": 22, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320667602, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320667606, "dur": 117, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320667728, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320667731, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320667751, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320667754, "dur": 130, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320667889, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320667893, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320667934, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320667945, "dur": 54, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320668004, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320668007, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320668035, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320668039, "dur": 130, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320668174, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320668179, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320668210, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320668214, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320668240, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320668243, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320668269, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320668273, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320668292, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320668295, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320668319, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320668324, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320668360, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320668365, "dur": 23, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320668392, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320668396, "dur": 52, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320668453, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320668457, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320668478, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320668482, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320668515, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320668519, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320668553, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320668560, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320668581, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320668585, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320668610, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320668613, "dur": 43, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320668662, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320668666, "dur": 70, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320668741, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320668745, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320668779, "dur": 3, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320668783, "dur": 25, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320668813, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320668817, "dur": 23, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320668845, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320668849, "dur": 32, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320668884, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320668887, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320668908, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320668912, "dur": 190, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320669106, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320669109, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320669126, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320669129, "dur": 75, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320669209, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320669213, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320669247, "dur": 3, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320669252, "dur": 73, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320669330, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320669333, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320669355, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320669359, "dur": 25, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320669389, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320669393, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320669425, "dur": 3, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320669431, "dur": 28, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320669462, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320669466, "dur": 21, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320669491, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320669494, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320669521, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320669526, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320669558, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320669562, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320669589, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320669594, "dur": 20, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320669616, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320669620, "dur": 34, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320669660, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320669664, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320669699, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320669704, "dur": 34, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320669744, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320669749, "dur": 40, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320669792, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320669795, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320669819, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320669832, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320669867, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320669873, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320669901, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320669904, "dur": 130, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320670039, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320670042, "dur": 134, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320670182, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320670188, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320670221, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320670226, "dur": 54, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320670286, "dur": 6, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320670294, "dur": 85, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320670382, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320670388, "dur": 100, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320670493, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320670498, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320670531, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320670536, "dur": 35, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320670576, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320670580, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320670606, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320670610, "dur": 133, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320670752, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320670757, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320670795, "dur": 3, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320670801, "dur": 29, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320670834, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320670840, "dur": 97, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320670944, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320670948, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320670979, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320670984, "dur": 58, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320671047, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320671051, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320671083, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320671087, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320671105, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320671108, "dur": 114, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320671228, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320671232, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320671263, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320671269, "dur": 28, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320671301, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320671305, "dur": 15, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320671321, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320671324, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320671347, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320671353, "dur": 46, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320671404, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320671408, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320671428, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320671431, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320671457, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320671461, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320671486, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320671491, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320671523, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320671528, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320671556, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320671559, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320671578, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320671581, "dur": 17, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320671604, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320671609, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320671644, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320671648, "dur": 271, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320671924, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320671931, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320671954, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320671958, "dur": 25, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320671988, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320671993, "dur": 18, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320672013, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320672016, "dur": 64, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320672085, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320672089, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320672134, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320672138, "dur": 40, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320672183, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320672188, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320672223, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320672228, "dur": 49, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320672283, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320672286, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320672318, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320672324, "dur": 77, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320672405, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320672408, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320672436, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320672442, "dur": 22, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320672470, "dur": 38, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320672512, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320672550, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320672555, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320672584, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320672588, "dur": 22, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320672614, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320672618, "dur": 17, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320672638, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320672642, "dur": 20, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320672668, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320672673, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320672705, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320672709, "dur": 114, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320672829, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320672832, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320672865, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320672869, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320672895, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320672899, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320672925, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320672929, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320672952, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320672956, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320672973, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320672976, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320673007, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320673018, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320673045, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320673055, "dur": 95, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320673154, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320673157, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320673181, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320673185, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320673204, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320673207, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320673233, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320673237, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320673266, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320673269, "dur": 19, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320673292, "dur": 5, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320673303, "dur": 30, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320673336, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320673340, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320673373, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320673377, "dur": 191, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320673573, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320673577, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320673596, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320673600, "dur": 73, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320673676, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320673680, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320673711, "dur": 3, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320673717, "dur": 35, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320673757, "dur": 3, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320673763, "dur": 71, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320673840, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320673847, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320673881, "dur": 817, "ph": "X", "name": "ProcessMessages 24", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320674704, "dur": 49, "ph": "X", "name": "ReadAsync 24", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320674759, "dur": 7, "ph": "X", "name": "ProcessMessages 288", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320674768, "dur": 90, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320674865, "dur": 4, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320674871, "dur": 95, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320674974, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320674980, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320675018, "dur": 3, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320675030, "dur": 304, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320675343, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320675348, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320675373, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320675377, "dur": 81, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320675463, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320675468, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320675511, "dur": 3, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320675517, "dur": 55, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320675578, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320675582, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320675606, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320675610, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320675635, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320675638, "dur": 57, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320675703, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320675708, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320675734, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320675738, "dur": 14, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320675754, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320675757, "dur": 169, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320675930, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320675933, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320675961, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320675965, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320675992, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320675995, "dur": 64, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320676065, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320676069, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320676093, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320676097, "dur": 30, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320676135, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320676139, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320676155, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320676158, "dur": 54, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320676218, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320676222, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320676271, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320676276, "dur": 32, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320676312, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320676316, "dur": 203, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320676531, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320676535, "dur": 103, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320676647, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320676652, "dur": 482, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320677140, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320677144, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320677180, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320677185, "dur": 50, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320677241, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320677245, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320677279, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320677283, "dur": 590, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320677880, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320677885, "dur": 88, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320677979, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320677984, "dur": 34, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320678022, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320678026, "dur": 92, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320678126, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320678132, "dur": 209, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320678348, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320678352, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320678396, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320678400, "dur": 119, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320678524, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320678528, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320678570, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320678573, "dur": 277, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320678858, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320678863, "dur": 85, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320678951, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320678954, "dur": 1016, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320679978, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320679984, "dur": 67, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320680055, "dur": 5, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320680062, "dur": 41109, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320721187, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320721194, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320721233, "dur": 1738, "ph": "X", "name": "ProcessMessages 206", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320722982, "dur": 6506, "ph": "X", "name": "ReadAsync 206", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320729507, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320729514, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320729552, "dur": 5, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320729559, "dur": 342, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320729906, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320729909, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320729928, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320729931, "dur": 258, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320730196, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320730201, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320730227, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320730231, "dur": 271, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320730508, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320730513, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320730536, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320730540, "dur": 1186, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320731734, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320731740, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320731767, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320731777, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320731811, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320731816, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320731841, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320731844, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320731875, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320731880, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320731907, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320731912, "dur": 220, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320732135, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320732139, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320732172, "dur": 3, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320732177, "dur": 72, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320732255, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320732259, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320732281, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320732285, "dur": 308, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320732596, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320732599, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320732631, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320732636, "dur": 210, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320732851, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320732856, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320732887, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320732891, "dur": 501, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320733400, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320733405, "dur": 104, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320733516, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320733523, "dur": 10210, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320743743, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320743751, "dur": 73, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320743827, "dur": 22, "ph": "X", "name": "ProcessMessages 1316", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320743852, "dur": 29, "ph": "X", "name": "ReadAsync 1316", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320743887, "dur": 5, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320743895, "dur": 23, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320743921, "dur": 3, "ph": "X", "name": "ProcessMessages 156", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320743927, "dur": 23, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320743960, "dur": 4, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320743972, "dur": 39, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320744015, "dur": 5, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320744023, "dur": 30, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320744058, "dur": 5, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320744066, "dur": 24, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320744093, "dur": 4, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320744099, "dur": 31, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320744135, "dur": 3, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320744140, "dur": 34, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320744180, "dur": 6, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320744189, "dur": 27, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320744220, "dur": 4, "ph": "X", "name": "ProcessMessages 204", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320744227, "dur": 23, "ph": "X", "name": "ReadAsync 204", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320744253, "dur": 4, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320744260, "dur": 25, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320744288, "dur": 4, "ph": "X", "name": "ProcessMessages 188", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320744295, "dur": 31, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320744331, "dur": 4, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320744338, "dur": 21, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320744361, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320744364, "dur": 91, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320744460, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320744464, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320744501, "dur": 3, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320744507, "dur": 141, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320744655, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320744659, "dur": 89, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320744754, "dur": 3, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320744758, "dur": 152, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320744916, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320744920, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320744962, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320744965, "dur": 252, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320745222, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320745225, "dur": 100, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320745332, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320745335, "dur": 213612, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320958959, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320958966, "dur": 99, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320959074, "dur": 39, "ph": "X", "name": "ProcessMessages 1944", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320959117, "dur": 10167, "ph": "X", "name": "ReadAsync 1944", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320969296, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320969302, "dur": 102, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320969411, "dur": 1846, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454320971271, "dur": 70966, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454321042251, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454321042258, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454321042310, "dur": 6, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454321042319, "dur": 53472, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454321095808, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454321095815, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454321095854, "dur": 33, "ph": "X", "name": "ProcessMessages 373", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454321095891, "dur": 14508, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454321110427, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454321110434, "dur": 179, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454321110620, "dur": 5, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454321110627, "dur": 39503, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454321150146, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454321150152, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454321150189, "dur": 34, "ph": "X", "name": "ProcessMessages 521", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454321150226, "dur": 9861, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454321160099, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454321160105, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454321160133, "dur": 27, "ph": "X", "name": "ProcessMessages 527", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454321160162, "dur": 7959, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454321168133, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454321168140, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454321168173, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454321168179, "dur": 857, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454321169044, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454321169048, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454321169072, "dur": 28, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454321169102, "dur": 7868, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454321176992, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454321176997, "dur": 72, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454321177072, "dur": 3, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454321177077, "dur": 227, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454321177310, "dur": 7, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454321177325, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454321177371, "dur": 21, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454321177394, "dur": 146, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454321177544, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454321177547, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454321177564, "dur": 15, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454321177580, "dur": 64401, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454321242008, "dur": 17, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454321242027, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454321242089, "dur": 6, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454321242096, "dur": 633, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454321242737, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454321242742, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454321242794, "dur": 35, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454321242832, "dur": 717, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454321243558, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454321243574, "dur": 106, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454321243687, "dur": 688, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 15424, "tid": 12884901888, "ts": 1751454321244381, "dur": 29089, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 15424, "tid": 4067, "ts": 1751454321286886, "dur": 5246, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 15424, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 15424, "tid": 8589934592, "ts": 1751454320611443, "dur": 292483, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 15424, "tid": 8589934592, "ts": 1751454320903929, "dur": 9, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 15424, "tid": 8589934592, "ts": 1751454320903939, "dur": 1169, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 15424, "tid": 4067, "ts": 1751454321292136, "dur": 18, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 15424, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 15424, "tid": 4294967296, "ts": 1751454320591145, "dur": 683622, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 15424, "tid": 4294967296, "ts": 1751454320596110, "dur": 8339, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 15424, "tid": 4294967296, "ts": 1751454321275021, "dur": 4699, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 15424, "tid": 4294967296, "ts": 1751454321277805, "dur": 120, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 15424, "tid": 4294967296, "ts": 1751454321279812, "dur": 18, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 15424, "tid": 4067, "ts": 1751454321292157, "dur": 18, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1751454320619946, "dur": 1881, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751454320621835, "dur": 1880, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751454320623845, "dur": 158, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1751454320624004, "dur": 386, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751454320625333, "dur": 565, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_4B40DB5AF1CE9D22.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751454320626586, "dur": 2529, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751454320634018, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751454320634200, "dur": 188, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751454320634449, "dur": 245, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751454320635883, "dur": 81, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InternalAPIEngineBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751454320636896, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Aseprite.Common.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751454320637416, "dur": 82, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751454320639441, "dur": 75, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751454320642446, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5934182079298241566.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751454320644430, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/spine-csharp.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751454320644860, "dur": 81, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/ScriptablePacker.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751454320645236, "dur": 88, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10678363927977808685.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751454320645615, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Burst.CodeGen.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751454320646106, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1751454320651828, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Runtime.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751454320624436, "dur": 30548, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751454320655001, "dur": 589103, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751454321244105, "dur": 506, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751454321244793, "dur": 55, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751454321244863, "dur": 23443, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1751454320624716, "dur": 30291, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751454320655026, "dur": 240, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751454320655277, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_D8154579280ABE30.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751454320655515, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_CD971E07FD450EB6.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751454320655611, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_B70C9B9527050002.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751454320655870, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_E2C1B1DA45896D6E.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751454320656187, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751454320656663, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751454320656916, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751454320656978, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751454320657150, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Path.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751454320657309, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751454320657619, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751454320657767, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751454320657890, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751454320657963, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751454320658224, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14262630423516740202.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751454320658442, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751454320658658, "dur": 706, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751454320659364, "dur": 719, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751454320660083, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751454320660290, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751454320660544, "dur": 695, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751454320661239, "dur": 311, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751454320661550, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751454320661789, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751454320662025, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751454320662245, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751454320662498, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751454320662705, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751454320662915, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751454320663163, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751454320663368, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751454320663590, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751454320663790, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751454320664036, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751454320664233, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751454320664442, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751454320664714, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751454320664927, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751454320665168, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751454320665382, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751454320665587, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751454320665826, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751454320666034, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751454320666299, "dur": 575, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751454320666889, "dur": 968, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751454320667857, "dur": 686, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751454320668546, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751454320668688, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751454320668787, "dur": 646, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751454320669434, "dur": 240, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751454320669728, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751454320669904, "dur": 777, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751454320670715, "dur": 242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751454320670987, "dur": 1117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751454320672168, "dur": 407, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751454320672575, "dur": 284, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751454320672863, "dur": 538, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751454320673402, "dur": 263, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751454320673670, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/OutlineFx.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751454320673882, "dur": 465, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/NativeFilePicker.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751454320674348, "dur": 223, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751454320674684, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751454320674749, "dur": 743, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751454320675493, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/OutlineFx.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751454320675622, "dur": 311, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/OutlineFx.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751454320675966, "dur": 375, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/OutlineFx.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751454320676394, "dur": 3339, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751454320679734, "dur": 48761, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751454320728496, "dur": 2689, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Tilemap.Extras.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751454320731186, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751454320731288, "dur": 2197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751454320733521, "dur": 2514, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751454320736089, "dur": 1906, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751454320738039, "dur": 2286, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Common.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751454320740354, "dur": 1800, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751454320742155, "dur": 614, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751454320742774, "dur": 2429, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/OutlineFx.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751454320745414, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751454320745684, "dur": 937, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751454320746621, "dur": 497484, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751454320624901, "dur": 30139, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751454320655042, "dur": 251, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_3122CFBB2DC47329.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751454320655337, "dur": 234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_05122CAABA8E3B59.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751454320655589, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_A5513E9150BC6315.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751454320655863, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_3C70E268A86B5279.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751454320656028, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751454320656146, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751454320656203, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751454320656265, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751454320656417, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751454320656658, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751454320656797, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751454320657015, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751454320657287, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751454320657639, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751454320657886, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751454320657971, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751454320658091, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10054408285822421624.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751454320658433, "dur": 87, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15875789592854681416.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751454320658521, "dur": 264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751454320658785, "dur": 921, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751454320659706, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751454320659934, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751454320660133, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751454320660337, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751454320660574, "dur": 795, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751454320661370, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751454320661585, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751454320661802, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751454320662044, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751454320662263, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751454320662497, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751454320662696, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751454320662901, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751454320663127, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751454320663333, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751454320663564, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751454320663769, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751454320664078, "dur": 544, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@24b10291b18f\\Runtime\\TMP\\TMP_UpdateRegistery.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751454320663968, "dur": 778, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751454320664746, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751454320664957, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751454320665192, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751454320665405, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751454320665598, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751454320665852, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751454320666059, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751454320666285, "dur": 83, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751454320666369, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751454320666597, "dur": 520, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751454320667117, "dur": 736, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751454320667881, "dur": 700, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751454320668582, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751454320668733, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751454320668896, "dur": 540, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751454320669437, "dur": 509, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751454320669951, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751454320670190, "dur": 1059, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751454320671250, "dur": 384, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751454320671676, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751454320671898, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751454320672150, "dur": 607, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751454320672792, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751454320672972, "dur": 651, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751454320673624, "dur": 365, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751454320674047, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751454320674197, "dur": 197, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751454320674399, "dur": 484, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751454320674883, "dur": 212, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751454320675121, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751454320675285, "dur": 399, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751454320675723, "dur": 433, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751454320676156, "dur": 585, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751454320676743, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751454320676959, "dur": 488, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751454320677526, "dur": 2213, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751454320679739, "dur": 48755, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751454320728495, "dur": 2320, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/OutlineFx.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751454320730855, "dur": 2443, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751454320733302, "dur": 668, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751454320733979, "dur": 2604, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/spine-unity.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751454320736583, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751454320736737, "dur": 2346, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Common.Path.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751454320739084, "dur": 636, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751454320739725, "dur": 2355, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Sprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751454320742118, "dur": 2302, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Aseprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751454320744466, "dur": 2078, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.SpriteShape.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751454320746607, "dur": 497523, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751454320624902, "dur": 30144, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751454320655048, "dur": 232, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_98CF6329D3D6F9AB.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751454320655425, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_878D99D4DAFDE3CC.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751454320655612, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_34D7E2BEC34818D5.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751454320655712, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751454320656191, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751454320656258, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751454320656422, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751454320656613, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751454320656969, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751454320657305, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751454320657515, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751454320657614, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751454320657737, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751454320657855, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751454320658023, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751454320658217, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/18297721446069157131.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751454320658466, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751454320658729, "dur": 978, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751454320659708, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751454320659948, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751454320660172, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751454320660394, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751454320660631, "dur": 599, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751454320661231, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751454320661464, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751454320661689, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751454320661910, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751454320662134, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751454320662355, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751454320662584, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751454320662780, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751454320662998, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751454320663206, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751454320663428, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751454320663651, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751454320663860, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751454320664096, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751454320664300, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751454320664519, "dur": 309, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751454320664828, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751454320665042, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751454320665275, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751454320665481, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751454320665677, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751454320665899, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751454320666110, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751454320666346, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751454320666580, "dur": 332, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751454320666912, "dur": 953, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751454320667865, "dur": 688, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751454320668554, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Postprocessing.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751454320668710, "dur": 678, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751454320669394, "dur": 767, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Postprocessing.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751454320670215, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751454320670274, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Postprocessing.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751454320670494, "dur": 605, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Postprocessing.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751454320671100, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751454320671258, "dur": 639, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Extras.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751454320671944, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751454320672131, "dur": 562, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751454320672694, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751454320672830, "dur": 641, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751454320673471, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751454320673594, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/NativeFilePicker.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751454320673791, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/spine-unity-editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751454320673956, "dur": 549, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/spine-unity-editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751454320674543, "dur": 516, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751454320675096, "dur": 1070, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751454320676166, "dur": 3563, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751454320679730, "dur": 48758, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751454320728492, "dur": 3003, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751454320731496, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751454320731571, "dur": 2332, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751454320733903, "dur": 325, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751454320734235, "dur": 2558, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751454320736793, "dur": 1252, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751454320738050, "dur": 2351, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751454320740402, "dur": 543, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751454320740950, "dur": 2349, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualEffectGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751454320743300, "dur": 1347, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751454320744687, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751454320744785, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751454320744970, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751454320745180, "dur": 201, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Mathematics.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751454320745479, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Common.Path.Editor.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751454320745558, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/PPv2URPConverters.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1751454320745670, "dur": 169, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751454320745865, "dur": 498237, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751454320624908, "dur": 30144, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751454320655055, "dur": 234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_AAD14E71D524EA60.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751454320655307, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_966AC74B818A5F90.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751454320655512, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_366576C77F483F0B.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751454320655582, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_DA3F53EC74158B55.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751454320655644, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751454320656128, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751454320656201, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751454320656303, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751454320656439, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751454320656793, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751454320656941, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.Shared.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751454320657098, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751454320657321, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751454320657641, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751454320657785, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751454320657965, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751454320658171, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12546371273225361319.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751454320658446, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751454320658674, "dur": 959, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751454320659633, "dur": 497, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751454320660130, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751454320660340, "dur": 258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751454320660598, "dur": 655, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751454320661253, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751454320661493, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751454320661726, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751454320661952, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751454320662162, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751454320662379, "dur": 622, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751454320663001, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751454320663231, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751454320663443, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751454320663700, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751454320663919, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751454320664166, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751454320664382, "dur": 328, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751454320664710, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751454320664923, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751454320665154, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751454320665355, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751454320665556, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751454320665792, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751454320666005, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751454320666215, "dur": 429, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751454320666644, "dur": 629, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751454320667273, "dur": 588, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751454320667861, "dur": 845, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751454320668707, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.PixelPerfect.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751454320668833, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751454320668893, "dur": 1168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.PixelPerfect.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751454320670062, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751454320670201, "dur": 484, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/spine-unity.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751454320670686, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751454320670771, "dur": 647, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751454320671464, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751454320671604, "dur": 1575, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/spine-unity.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751454320673227, "dur": 156, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/spine-unity.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751454320673384, "dur": 1029, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751454320674413, "dur": 238, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751454320674702, "dur": 614, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751454320675317, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751454320675479, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751454320675593, "dur": 500, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751454320676165, "dur": 3559, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751454320679724, "dur": 51356, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751454320731082, "dur": 3667, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Assembly-CSharp-firstpass.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751454320734782, "dur": 2330, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751454320737113, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751454320737193, "dur": 2336, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751454320739530, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751454320739593, "dur": 2188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/ScriptablePacker.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751454320741782, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751454320741884, "dur": 2154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/spine-unity-editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751454320744039, "dur": 593, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751454320744663, "dur": 430, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751454320745168, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751454320745357, "dur": 203, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751454320745560, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/NativeFilePicker.Editor.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1751454320745700, "dur": 432521, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751454321178238, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aEDbg.dag\\post-processed\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 4, "ts": 1751454321178221, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 4, "ts": 1751454321178313, "dur": 621, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 4, "ts": 1751454321178936, "dur": 65173, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751454320624818, "dur": 30210, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751454320655031, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_7B22B0BDD3082718.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751454320655288, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_24CD4430EA38EE97.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751454320655371, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_692349F0772110A2.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751454320655511, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_88313D863DE50351.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751454320655633, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_099E8E7D5E4735AD.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751454320655744, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_7D6DB1EEB95EE9BC.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751454320656029, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751454320656169, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751454320656221, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751454320656521, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751454320656670, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751454320656784, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751454320656872, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751454320657072, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751454320657221, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751454320657314, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751454320657383, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751454320657515, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751454320657582, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751454320657645, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751454320657929, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751454320658066, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751454320658259, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751454320658318, "dur": 283, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3925285597375108267.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751454320658612, "dur": 276, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751454320660109, "dur": 547, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.36f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Microsoft.AspNetCore.HttpLogging.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751454320658888, "dur": 1802, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751454320660690, "dur": 591, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751454320661282, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751454320661538, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751454320661765, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751454320662008, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751454320662395, "dur": 1116, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@911117698220\\Editor\\Data\\Interfaces\\PositionSource.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751454320662237, "dur": 1324, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751454320663561, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751454320663772, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751454320664121, "dur": 1095, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@24b10291b18f\\Runtime\\TMP\\TMP_ScrollbarEventHandler.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751454320664018, "dur": 1294, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751454320665313, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751454320665512, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751454320665730, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751454320665941, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751454320666145, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751454320666348, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751454320666601, "dur": 785, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751454320667386, "dur": 506, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751454320667892, "dur": 1176, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751454320669069, "dur": 1035, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751454320670138, "dur": 854, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751454320670995, "dur": 837, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751454320671879, "dur": 1398, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751454320673278, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751454320673363, "dur": 1695, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751454320675086, "dur": 1085, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751454320676171, "dur": 3551, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751454320679723, "dur": 48767, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751454320728492, "dur": 2299, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751454320730792, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751454320730852, "dur": 3273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751454320734125, "dur": 493, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751454320734626, "dur": 3075, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751454320737702, "dur": 653, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751454320738361, "dur": 3102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.PixelPerfect.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751454320741463, "dur": 321, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751454320741790, "dur": 2804, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751454320744632, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751454320744801, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751454320744930, "dur": 241, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751454320745332, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751454320745431, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751454320745712, "dur": 498389, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751454320624726, "dur": 30291, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751454320655026, "dur": 255, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751454320655287, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_24AD401155842AFB.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751454320655508, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_C35DFDA63C563B2E.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751454320655584, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_E951570DA9D21231.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751454320655877, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_62645ED13E051483.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751454320656015, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751454320656160, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751454320656325, "dur": 235, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751454320656598, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751454320656877, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751454320656950, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751454320657044, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Animation.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1751454320657145, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751454320657287, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751454320657516, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rider.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751454320657631, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751454320657757, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751454320657833, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751454320657919, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751454320658056, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751454320658463, "dur": 314, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751454320658777, "dur": 836, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751454320659613, "dur": 912, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751454320660525, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751454320660771, "dur": 623, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751454320661394, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751454320661618, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751454320661843, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751454320662093, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751454320662322, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751454320662559, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751454320662756, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751454320663010, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751454320663244, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751454320663478, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751454320663686, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751454320663901, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751454320664152, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751454320664369, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751454320664640, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751454320664870, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751454320665070, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751454320665301, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751454320665510, "dur": 278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751454320665789, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751454320665991, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751454320666225, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751454320666471, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751454320666891, "dur": 978, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751454320667870, "dur": 667, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751454320668539, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751454320668675, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751454320668749, "dur": 1786, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751454320670536, "dur": 293, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751454320670860, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751454320671051, "dur": 1186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751454320672237, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751454320672479, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751454320672674, "dur": 580, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751454320673255, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751454320673349, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751454320673462, "dur": 105, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.GPUDriven.Runtime.ref.dll_94DF45657C08BC0C.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751454320673587, "dur": 309, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751454320673896, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751454320674066, "dur": 1156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751454320675223, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751454320675313, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751454320675481, "dur": 1524, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751454320677006, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751454320677134, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualEffectGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751454320677314, "dur": 1090, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualEffectGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751454320678405, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751454320678485, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751454320678621, "dur": 579, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751454320679272, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.PixelPerfect.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751454320679384, "dur": 279, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.PixelPerfect.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751454320679718, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp-firstpass.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751454320679869, "dur": 336, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp-firstpass.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751454320680231, "dur": 505, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751454320681204, "dur": 91, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751454320681834, "dur": 278465, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751454320964947, "dur": 5117, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aEDbg.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751454320964621, "dur": 5534, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751454320970467, "dur": 69, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751454320970975, "dur": 126170, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751454321101003, "dur": 8209, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aEDbg.dag\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751454321100994, "dur": 9421, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751454321111484, "dur": 171, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751454321111899, "dur": 49556, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751454321178225, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aEDbg.dag\\post-processed\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1751454321178218, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1751454321178307, "dur": 385, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1751454321178696, "dur": 65414, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751454320624792, "dur": 30230, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751454320655025, "dur": 269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_7A744375D0C40DCD.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751454320655315, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_47EFA976E901FEC1.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751454320655400, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_5AE8CAD8193F1AE4.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751454320655522, "dur": 216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_3EA836CAC6D1710A.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751454320655762, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_4DE7717F601CC2DC.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751454320656141, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751454320656211, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751454320656339, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751454320656656, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751454320656738, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751454320656876, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751454320656952, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751454320657020, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751454320657316, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751454320657450, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751454320657524, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Psdimporter.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751454320657606, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751454320657821, "dur": 252, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751454320658448, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751454320658665, "dur": 847, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751454320659513, "dur": 647, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751454320660160, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751454320660381, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751454320660595, "dur": 601, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751454320661197, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751454320661447, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751454320661661, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751454320661874, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751454320662103, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751454320662317, "dur": 522, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751454320662839, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751454320663077, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751454320663286, "dur": 750, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751454320664036, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751454320664249, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751454320664541, "dur": 626, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1b53f46e931b\\Runtime\\VisualScripting.Core\\Reflection\\Optimization\\Func_5.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751454320664465, "dur": 833, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751454320665298, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751454320665498, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751454320665700, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751454320665929, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751454320666138, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751454320666362, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751454320666579, "dur": 313, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751454320666892, "dur": 974, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751454320667866, "dur": 673, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751454320668542, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InternalAPIEngineBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751454320668674, "dur": 917, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751454320669592, "dur": 127, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InternalAPIEngineBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751454320669720, "dur": 1039, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InternalAPIEngineBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751454320670760, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751454320670881, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751454320671080, "dur": 1484, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751454320672565, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751454320672626, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751454320672784, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751454320672962, "dur": 742, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751454320673705, "dur": 597, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751454320674326, "dur": 217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751454320674543, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751454320674629, "dur": 785, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751454320675490, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Animation.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751454320675649, "dur": 430, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Animation.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751454320676152, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751454320676313, "dur": 474, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751454320676854, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Aseprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751454320677003, "dur": 453, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Aseprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751454320677504, "dur": 2224, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751454320679728, "dur": 48756, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751454320728485, "dur": 2370, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.SpriteShape.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751454320730860, "dur": 2677, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751454320733544, "dur": 2720, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualEffectGraph.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751454320736265, "dur": 216, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751454320736486, "dur": 2379, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Tilemap.Extras.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751454320738865, "dur": 512, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751454320739382, "dur": 2426, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Postprocessing.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751454320741809, "dur": 811, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751454320742624, "dur": 2331, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751454320745052, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751454320745179, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-firstpass.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751454320745404, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751454320745686, "dur": 160837, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751454320908209, "dur": 233, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 7, "ts": 1751454320908443, "dur": 1108, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 7, "ts": 1751454320909552, "dur": 64, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 7, "ts": 1751454320906525, "dur": 3094, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751454320909619, "dur": 334484, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751454320624888, "dur": 30147, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751454320655038, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_89517EAC2A874601.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751454320655310, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_F01F6C04D397C746.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751454320655507, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_D3788CF92C3C660B.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751454320655642, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751454320656180, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751454320656261, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751454320656406, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751454320656516, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751454320656632, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751454320656845, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751454320657301, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751454320657400, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751454320657521, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751454320657626, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751454320657772, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751454320657925, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751454320658002, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751454320658453, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751454320658700, "dur": 1061, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751454320659762, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751454320659974, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751454320660172, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751454320660397, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751454320660628, "dur": 635, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751454320661263, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751454320661492, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751454320661710, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751454320661964, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751454320662216, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751454320662461, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751454320662663, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751454320662861, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751454320663081, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751454320663294, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751454320663515, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751454320663743, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751454320663958, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751454320664199, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751454320664404, "dur": 294, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751454320664698, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751454320664917, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751454320665124, "dur": 1286, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.postprocessing@026b2a0827a4\\PostProcessing\\Runtime\\Monitors\\HistogramMonitor.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751454320665124, "dur": 1499, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751454320666623, "dur": 661, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751454320667284, "dur": 570, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751454320667882, "dur": 653, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751454320668536, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751454320668745, "dur": 712, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751454320669457, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751454320669622, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751454320669836, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751454320670021, "dur": 1273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751454320671295, "dur": 836, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751454320672139, "dur": 506, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751454320672646, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751454320672719, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751454320672923, "dur": 2106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751454320675030, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751454320675108, "dur": 1059, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751454320676167, "dur": 3551, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751454320679720, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751454320679940, "dur": 48556, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751454320728497, "dur": 2319, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751454320730861, "dur": 2322, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751454320733184, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751454320733249, "dur": 2417, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751454320735667, "dur": 950, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751454320736622, "dur": 2357, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/PsdPlugin.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751454320738980, "dur": 2229, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751454320741214, "dur": 2262, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751454320743530, "dur": 2274, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751454320745857, "dur": 498249, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751454320624995, "dur": 30064, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751454320655061, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_D07FFB3B2770754A.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751454320655278, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_385F174668EB898F.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751454320655419, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_EB030C3D9E9F35E6.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751454320655505, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_847E2D6319A6BEA0.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751454320655638, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751454320655705, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_634E261A11766DA3.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751454320656224, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751454320656579, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ShaderGraph.Utilities.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1751454320656685, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751454320656790, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751454320656978, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751454320657617, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751454320657815, "dur": 276, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751454320658093, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/18127688178268093734.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751454320658240, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6433205971168649903.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751454320658425, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9680343299601325691.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751454320658493, "dur": 307, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751454320658801, "dur": 1158, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751454320659959, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751454320660182, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751454320660420, "dur": 338, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751454320660758, "dur": 615, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751454320661374, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751454320661607, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751454320661828, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751454320662090, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751454320662310, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751454320662548, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751454320663473, "dur": 546, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@a394fe607f89\\Runtime\\Decal\\ScreenSpace\\DecalGBufferRenderPass.cs"}}, {"pid": 12345, "tid": 9, "ts": 1751454320662753, "dur": 1315, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751454320664069, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751454320664288, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751454320664498, "dur": 274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751454320664772, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751454320664973, "dur": 287, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751454320665260, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751454320665473, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751454320665696, "dur": 340, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751454320666036, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751454320666226, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751454320666451, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751454320666650, "dur": 344, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751454320666994, "dur": 898, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751454320667892, "dur": 825, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751454320668718, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751454320668888, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Path.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751454320669044, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751454320669111, "dur": 615, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Path.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751454320669726, "dur": 273, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751454320670003, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/PsdPlugin.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751454320670204, "dur": 503, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/PsdPlugin.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751454320670747, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751454320670964, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Aseprite.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751454320671184, "dur": 503, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Aseprite.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751454320671687, "dur": 1302, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751454320672990, "dur": 316, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Aseprite.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751454320673309, "dur": 237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Aseprite.Common.ref.dll_68B89EFD6B853D19.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751454320673590, "dur": 458, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751454320674049, "dur": 292, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751454320674362, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/ScriptablePacker.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751454320674545, "dur": 637, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/ScriptablePacker.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751454320675216, "dur": 938, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751454320676155, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.IK.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751454320676330, "dur": 383, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.IK.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751454320676778, "dur": 2959, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751454320679737, "dur": 48762, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751454320728504, "dur": 2299, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Animation.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751454320730804, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751454320730870, "dur": 2602, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751454320733525, "dur": 2400, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751454320735925, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751454320736003, "dur": 2269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.IK.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751454320738315, "dur": 1927, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751454320740278, "dur": 2051, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751454320742329, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751454320742497, "dur": 2389, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.InternalAPIEditorBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751454320744886, "dur": 327, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751454320745696, "dur": 218971, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751454320964687, "dur": 76246, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aEDbg.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 9, "ts": 1751454320964669, "dur": 77602, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751454321043380, "dur": 196, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751454321043850, "dur": 107642, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751454321169324, "dur": 73873, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aEDbg.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 9, "ts": 1751454321169317, "dur": 73882, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 9, "ts": 1751454321243217, "dur": 843, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 10, "ts": 1751454320625117, "dur": 29964, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751454320655084, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Apple.Extensions.Common.dll_D4E020400338F5BB.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751454320655274, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751454320655342, "dur": 292, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_3713E40F818C5856.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751454320655650, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_239A276499DBD1EB.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751454320655864, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_173B2236195846C7.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751454320656190, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751454320656376, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751454320656640, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751454320656733, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751454320656865, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751454320656964, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751454320657071, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751454320657234, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751454320657474, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751454320657568, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751454320657638, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751454320657786, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751454320657981, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751454320658145, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751454320658450, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751454320658710, "dur": 997, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751454320659707, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751454320659923, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751454320660123, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751454320660339, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751454320660601, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751454320660806, "dur": 632, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751454320661438, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751454320661671, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751454320661896, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751454320662105, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751454320662335, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751454320662568, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751454320662762, "dur": 290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751454320663053, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751454320663274, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751454320663499, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751454320663711, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751454320663927, "dur": 306, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751454320664234, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751454320664449, "dur": 296, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751454320664746, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751454320664945, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751454320665200, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751454320665419, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751454320665635, "dur": 770, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751454320666406, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751454320666611, "dur": 765, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751454320667376, "dur": 491, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751454320667868, "dur": 679, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751454320668548, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751454320668710, "dur": 880, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751454320669722, "dur": 833, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751454320670600, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751454320670739, "dur": 3020, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751454320673760, "dur": 225, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751454320674029, "dur": 619, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751454320674679, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751454320674748, "dur": 728, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751454320675477, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751454320675645, "dur": 454, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751454320676162, "dur": 720, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751454320676883, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Psdimporter.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751454320677022, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751454320677084, "dur": 520, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Psdimporter.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751454320677653, "dur": 2086, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751454320679740, "dur": 48784, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751454320728524, "dur": 2310, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/NativeFilePicker.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751454320730835, "dur": 1045, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751454320731887, "dur": 13437, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.IK.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751454320745364, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751454320745607, "dur": 81, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Common.pdb"}}, {"pid": 12345, "tid": 10, "ts": 1751454320745688, "dur": 163969, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751454320909657, "dur": 334462, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751454320625016, "dur": 30050, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751454320655068, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_21E5C8D6C2CA360E.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751454320655233, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_12F61435F0A80E5C.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751454320655309, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_1237F680F70DA85A.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751454320655501, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_76128D4AF776323F.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751454320655571, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_15728F368AD157F8.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751454320655847, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_0E5ABA1EF864C1B6.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751454320656072, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751454320656255, "dur": 9857, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751454320666200, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751454320666430, "dur": 1378, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751454320667881, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751454320668028, "dur": 406, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751454320668535, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751454320668678, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751454320668790, "dur": 919, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751454320669777, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751454320669982, "dur": 570, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751454320670620, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751454320670786, "dur": 2386, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751454320673172, "dur": 361, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751454320673533, "dur": 284, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751454320673825, "dur": 974, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 11, "ts": 1751454320674860, "dur": 179, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751454320675247, "dur": 47282, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 11, "ts": 1751454320728483, "dur": 2360, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751454320730877, "dur": 2186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751454320733115, "dur": 2238, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751454320735355, "dur": 514, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751454320735875, "dur": 2072, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751454320737974, "dur": 2167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751454320740142, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751454320740223, "dur": 2327, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751454320742551, "dur": 237, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751454320742793, "dur": 2437, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Animation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751454320745234, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751454320745556, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Collections.Editor.dll"}}, {"pid": 12345, "tid": 11, "ts": 1751454320745682, "dur": 566, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751454320746249, "dur": 497876, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751454320625031, "dur": 30041, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751454320655074, "dur": 221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Common.dll_D5DC31B4B0DA901E.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751454320655318, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_50CFC6D0BF59D948.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751454320655408, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_DAD3C4523F8649DE.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751454320655534, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_767627B5731476EF.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751454320655626, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_B72398FA1A1CE63D.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751454320655853, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_5099CB98C03D5F98.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751454320656149, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751454320656261, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751454320656355, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751454320656420, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751454320656868, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751454320656947, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualEffectGraph.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751454320657255, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751454320657361, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751454320657518, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Shaders.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751454320657614, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751454320657740, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751454320657832, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751454320657896, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751454320658444, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751454320658701, "dur": 857, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751454320659559, "dur": 629, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751454320660189, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751454320660423, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751454320660670, "dur": 609, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751454320661279, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751454320661522, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751454320661759, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751454320662021, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751454320662254, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751454320662491, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751454320662689, "dur": 404, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751454320663093, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751454320663314, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751454320663548, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751454320663773, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751454320664019, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751454320664236, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751454320664482, "dur": 628, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1b53f46e931b\\Runtime\\VisualScripting.Core\\Reflection\\Optimization\\ReflectionInvoker.cs"}}, {"pid": 12345, "tid": 12, "ts": 1751454320664459, "dur": 848, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751454320665307, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751454320665508, "dur": 293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751454320666201, "dur": 719, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@001b54a8988a\\Editor\\ExternalLink.cs"}}, {"pid": 12345, "tid": 12, "ts": 1751454320665801, "dur": 1129, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751454320666930, "dur": 935, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751454320667866, "dur": 679, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751454320668546, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751454320668712, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751454320668780, "dur": 1400, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751454320670181, "dur": 593, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751454320670804, "dur": 594, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InternalAPIEditorBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751454320671425, "dur": 640, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InternalAPIEditorBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751454320672066, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751454320672196, "dur": 1147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751454320673351, "dur": 601, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.SpriteShape.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751454320673990, "dur": 797, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.SpriteShape.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751454320674788, "dur": 286, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751454320675118, "dur": 1051, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751454320676169, "dur": 3551, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751454320679722, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751454320679878, "dur": 48614, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751454320728494, "dur": 2336, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751454320730831, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751454320730903, "dur": 2328, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751454320733275, "dur": 3676, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751454320736952, "dur": 401, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751454320737358, "dur": 3319, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751454320740708, "dur": 2932, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Aseprite.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751454320743641, "dur": 214, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751454320743860, "dur": 2321, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751454320746241, "dur": 497866, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751454320625230, "dur": 29871, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751454320655103, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_8AA31634E5830E2F.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751454320655221, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751454320655406, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_299E64FBD7A1C295.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751454320655462, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751454320655542, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_2AE2A6D2A1246318.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751454320655629, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751454320655842, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_E496E41FECA98925.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751454320656116, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751454320656241, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751454320656362, "dur": 9764, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751454320666242, "dur": 700, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751454320666942, "dur": 914, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751454320667856, "dur": 905, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751454320668763, "dur": 279, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751454320669043, "dur": 633, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751454320669680, "dur": 1203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751454320670884, "dur": 1441, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751454320672334, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751454320672461, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751454320672621, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751454320672715, "dur": 434, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751454320673193, "dur": 132, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751454320673326, "dur": 2073, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751454320675473, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751454320675620, "dur": 276, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751454320675899, "dur": 753, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751454320676653, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751454320676737, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751454320676962, "dur": 450, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751454320677462, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751454320677598, "dur": 274, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751454320677919, "dur": 1830, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751454320679749, "dur": 48773, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751454320728523, "dur": 2319, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1751454320730875, "dur": 2191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Common.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1751454320733067, "dur": 566, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751454320733640, "dur": 2963, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1751454320736637, "dur": 2661, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1751454320739298, "dur": 741, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751454320740046, "dur": 2541, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1751454320742588, "dur": 803, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751454320743398, "dur": 2502, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1751454320745901, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751454320745989, "dur": 498122, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751454320625132, "dur": 29958, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751454320655093, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_E4B698B849B24AEB.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751454320655245, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_7BCDC5BDF664862C.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751454320655428, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_60DEBCA51ABA1468.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751454320655623, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_4C3484A57A05DA64.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751454320656159, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751454320656239, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751454320656429, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751454320656507, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751454320656868, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751454320656962, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751454320657027, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751454320657120, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751454320657197, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Aseprite.Common.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1751454320657350, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751454320657599, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751454320657902, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751454320657993, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751454320658455, "dur": 259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751454320658714, "dur": 1234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751454320659948, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751454320660157, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751454320660368, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751454320660625, "dur": 597, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751454320661223, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751454320661460, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751454320661681, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751454320661930, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751454320662144, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751454320662368, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751454320662599, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751454320662820, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751454320663049, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751454320663458, "dur": 559, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1b53f46e931b\\Runtime\\VisualScripting.Flow\\Framework\\Math\\Scalar\\ScalarAbsolute.cs"}}, {"pid": 12345, "tid": 14, "ts": 1751454320663269, "dur": 772, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751454320664041, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751454320664251, "dur": 583, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751454320664834, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751454320665074, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751454320665316, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751454320665540, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751454320666013, "dur": 918, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@001b54a8988a\\Editor\\UI\\GetPlasticShortcut.cs"}}, {"pid": 12345, "tid": 14, "ts": 1751454320665752, "dur": 1273, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751454320667025, "dur": 832, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751454320667858, "dur": 1328, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751454320669188, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/spine-csharp.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751454320669369, "dur": 271, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751454320669646, "dur": 236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751454320669883, "dur": 235, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751454320670124, "dur": 649, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1751454320670814, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751454320670988, "dur": 662, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751454320671653, "dur": 534, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1751454320672188, "dur": 421, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751454320672615, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751454320672785, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751454320672877, "dur": 610, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1751454320673530, "dur": 282, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1751454320673841, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/NativeFilePicker.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751454320674022, "dur": 1350, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/NativeFilePicker.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1751454320675404, "dur": 749, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751454320676154, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Animation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751454320676297, "dur": 519, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Animation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1751454320676879, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.IK.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751454320677015, "dur": 303, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.IK.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1751454320677359, "dur": 2384, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751454320679743, "dur": 48762, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751454320728508, "dur": 2295, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Postprocessing.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1751454320730849, "dur": 5760, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/spine-csharp.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1751454320736610, "dur": 400, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751454320737016, "dur": 2332, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1751454320739377, "dur": 2251, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1751454320741632, "dur": 549, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751454320742186, "dur": 2220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1751454320744407, "dur": 290, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751454320744828, "dur": 267, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751454320745184, "dur": 356, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.Editor.pdb"}}, {"pid": 12345, "tid": 14, "ts": 1751454320745555, "dur": 142, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.PixelPerfect.pdb"}}, {"pid": 12345, "tid": 14, "ts": 1751454320745698, "dur": 423621, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751454321169339, "dur": 117, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aEDbg.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 14, "ts": 1751454321169320, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 14, "ts": 1751454321169489, "dur": 936, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 14, "ts": 1751454321170429, "dur": 73689, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751454320625212, "dur": 29884, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751454320655098, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_BA35B04A057142E1.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751454320655230, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_C32E3DE896B50E07.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751454320655297, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751454320655417, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_B5214F7A6C172998.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751454320655502, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_A01C5835F2B3673A.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751454320655579, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_41563AEB7BF951B8.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751454320655639, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751454320655854, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_4B40DB5AF1CE9D22.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751454320656167, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751454320656257, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751454320656424, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751454320656521, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751454320656875, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751454320656987, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751454320657145, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751454320657288, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751454320657444, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751454320657515, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Psdimporter.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1751454320657622, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751454320657748, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751454320657825, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751454320658079, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14324468880911302389.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1751454320658244, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/13226667538729057029.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1751454320658345, "dur": 81, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/13226667538729057029.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1751454320658427, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751454320658651, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751454320658841, "dur": 862, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751454320659703, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751454320659933, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751454320660132, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751454320660342, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751454320660592, "dur": 648, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751454320661241, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751454320661487, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751454320661707, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751454320661957, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751454320662197, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751454320662429, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751454320662636, "dur": 317, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751454320662954, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751454320663173, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751454320663388, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751454320663608, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751454320663967, "dur": 527, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1b53f46e931b\\Editor\\VisualScripting.Core\\Inspection\\Primitives\\UintInspector.cs"}}, {"pid": 12345, "tid": 15, "ts": 1751454320664596, "dur": 1130, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1b53f46e931b\\Editor\\VisualScripting.Core\\Inspection\\Primitives\\FloatInspector.cs"}}, {"pid": 12345, "tid": 15, "ts": 1751454320663822, "dur": 1905, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751454320665727, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751454320665981, "dur": 768, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.tilemap@91020cbfae56\\Editor\\UI\\TilemapEditorToolbarStrip.cs"}}, {"pid": 12345, "tid": 15, "ts": 1751454320665933, "dur": 1008, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751454320666941, "dur": 920, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751454320667861, "dur": 846, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751454320668708, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Extras.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751454320668908, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751454320668980, "dur": 543, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Extras.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751454320669524, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751454320669695, "dur": 964, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/spine-csharp.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751454320670660, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751454320670779, "dur": 283, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.SpriteShape.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751454320671063, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751454320671116, "dur": 509, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.SpriteShape.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751454320671687, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751454320671830, "dur": 823, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751454320672656, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751454320672833, "dur": 381, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751454320673214, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751454320673347, "dur": 553, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Extras.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751454320673962, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751454320674052, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751454320674207, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751454320674421, "dur": 503, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751454320674954, "dur": 1208, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751454320676162, "dur": 3114, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751454320679280, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751454320679406, "dur": 272, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751454320679727, "dur": 48759, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751454320728489, "dur": 2331, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1751454320730866, "dur": 4766, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1751454320735633, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751454320735711, "dur": 2268, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1751454320737980, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751454320738045, "dur": 2396, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Tilemap.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1751454320740442, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751454320740625, "dur": 2137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1751454320742762, "dur": 307, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751454320743099, "dur": 2555, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1751454320745715, "dur": 498384, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751454320625251, "dur": 29856, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751454320655107, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_7D19F4582D6FA60D.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751454320655236, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751454320655410, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_E3AA45DCF433BC79.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751454320655604, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_24D87271FC85A7AF.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751454320655843, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_26C00F9C5DF6EF10.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751454320656079, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751454320656217, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751454320656294, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751454320656423, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751454320656849, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751454320656967, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751454320657445, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751454320657640, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751454320657777, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751454320657895, "dur": 205, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751454320658285, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10927284454210886603.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1751454320658346, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10927284454210886603.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1751454320658457, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751454320658687, "dur": 968, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751454320659656, "dur": 147, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751454320659803, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751454320660023, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751454320660241, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751454320660488, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751454320660717, "dur": 587, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751454320661304, "dur": 285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751454320661589, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751454320661804, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751454320662060, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751454320662277, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751454320662510, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751454320662714, "dur": 341, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751454320663055, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751454320663256, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751454320663473, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751454320663681, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751454320663880, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751454320664136, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751454320664361, "dur": 275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751454320664637, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751454320664845, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751454320665099, "dur": 625, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Runtime\\DiscreteTime.cs"}}, {"pid": 12345, "tid": 16, "ts": 1751454320665053, "dur": 816, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751454320665869, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751454320666100, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751454320666336, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751454320666607, "dur": 841, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751454320667448, "dur": 415, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751454320667864, "dur": 674, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751454320668539, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751454320668692, "dur": 579, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751454320669276, "dur": 751, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751454320670028, "dur": 919, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751454320670992, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualEffectGraph.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751454320671221, "dur": 648, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualEffectGraph.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751454320671870, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751454320671975, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751454320672193, "dur": 474, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751454320672667, "dur": 868, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751454320673535, "dur": 699, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751454320674260, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751454320674403, "dur": 678, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751454320675087, "dur": 353, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751454320675478, "dur": 688, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751454320676166, "dur": 3568, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751454320679735, "dur": 51338, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751454320731075, "dur": 2096, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.InternalAPIEngineBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1751454320733204, "dur": 2727, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.PixelPerfect.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1751454320735932, "dur": 1801, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751454320737739, "dur": 2321, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/NativeFilePicker.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1751454320740061, "dur": 2559, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751454320742627, "dur": 2369, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Psdimporter.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1751454320744997, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751454320745176, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Psdimporter.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1751454320745304, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751454320745682, "dur": 315, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751454320745998, "dur": 498114, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751454321273169, "dur": 1357, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 15424, "tid": 4067, "ts": 1751454321292820, "dur": 2134, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 15424, "tid": 4067, "ts": 1751454321295094, "dur": 2117, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 15424, "tid": 4067, "ts": 1751454321284791, "dur": 13331, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}