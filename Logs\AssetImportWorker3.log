Using pre-set license
Built from '6000.0/staging' branch; Version is '6000.0.36f1 (9fe3b5f71dbb) revision 10478517'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 10  (10.0.19045) 64bit Professional' Language: 'pt' Physical Memory: 16221 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\6000.0.36f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker3
-projectPath
D:/Menino Autista/DKG-RPG-Mobile
-logFile
Logs/AssetImportWorker3.log
-srvPort
65077
-job-worker-count
7
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: D:/Menino Autista/DKG-RPG-Mobile
D:/Menino Autista/DKG-RPG-Mobile
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [19936]  Target information:

Player connection [19936]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 3459495301 [EditorId] 3459495301 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-J26I52U) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [19936] Host joined multi-casting on [***********:54997]...
Player connection [19936] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 7
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 2.07 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.36f1 (9fe3b5f71dbb)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/Menino Autista/DKG-RPG-Mobile/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce GTX 1050 Ti (ID=0x1c82)
    Vendor:   NVIDIA
    VRAM:     4004 MB
    Driver:   32.0.15.6094
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56064
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.dll
Registered in 0.003175 seconds.
- Loaded All Assemblies, in  0.351 seconds
[usbmuxd] Start listen thread
[usbmuxd] Listen thread started
Native extension for iOS target not found
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 178 ms
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.539 seconds
Domain Reload Profiling: 888ms
	BeginReloadAssembly (127ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (41ms)
	LoadAllAssembliesAndSetupDomain (141ms)
		LoadAssemblies (125ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (138ms)
			TypeCache.Refresh (136ms)
				TypeCache.ScanAssembly (125ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (539ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (497ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (278ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (53ms)
			ProcessInitializeOnLoadAttributes (108ms)
			ProcessInitializeOnLoadMethodAttributes (54ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.886 seconds
Refreshing native plugins compatible for Editor in 1.11 ms, found 4 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.882 seconds
Domain Reload Profiling: 1766ms
	BeginReloadAssembly (148ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (25ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (33ms)
	LoadAllAssembliesAndSetupDomain (660ms)
		LoadAssemblies (450ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (302ms)
			TypeCache.Refresh (228ms)
				TypeCache.ScanAssembly (204ms)
			BuildScriptInfoCaches (59ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (883ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (736ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (14ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (185ms)
			ProcessInitializeOnLoadAttributes (426ms)
			ProcessInitializeOnLoadMethodAttributes (99ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.01 seconds
Refreshing native plugins compatible for Editor in 1.05 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 324 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7564 unused Assets / (10.1 MB). Loaded Objects now: 8362.
Memory consumption went from 236.4 MB to 226.2 MB.
Total: 8.815300 ms (FindLiveObjects: 0.646900 ms CreateObjectMapping: 0.551400 ms MarkObjects: 3.996300 ms  DeleteObjects: 3.619900 ms)

========================================================================
Received Import Request.
  Time since last request: 90907.003669 seconds.
  path: Assets/Resources/Prefabs/DamageLabelElectricity.prefab
  artifactKey: Guid(e14da7c344d28694d83f19abab85690e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/DamageLabelElectricity.prefab using Guid(e14da7c344d28694d83f19abab85690e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '45466436c4203a59d4f1fbbe4dd77f1a') in 0.4578317 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 26

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Resources/Prefabs/DamageLabelPossession.prefab
  artifactKey: Guid(b46a5b7c4f52fda4fa43f7fae34529be) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/DamageLabelPossession.prefab using Guid(b46a5b7c4f52fda4fa43f7fae34529be) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a3398fe6c5e66e34fbfae91098d361cd') in 0.0367962 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 26

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Resources/Prefabs/DamageLabelStrength.prefab
  artifactKey: Guid(4ce31a05633ad174289e800ea35e02db) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/DamageLabelStrength.prefab using Guid(4ce31a05633ad174289e800ea35e02db) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '982d74f772c3797ed3adf00875f46c81') in 0.0307717 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 26

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Resources/Prefabs/DamageLabelCoin.prefab
  artifactKey: Guid(31f94c3dc423b0f4ea25fea123c649dd) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/DamageLabelCoin.prefab using Guid(31f94c3dc423b0f4ea25fea123c649dd) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '11e545251ffab820d010fc104ccd1247') in 0.0833049 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 26

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in 11.648 seconds
Refreshing native plugins compatible for Editor in 1.14 ms, found 4 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.632 seconds
Domain Reload Profiling: 13284ms
	BeginReloadAssembly (902ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (179ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (124ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (25ms)
	LoadAllAssembliesAndSetupDomain (10682ms)
		LoadAssemblies (10761ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (433ms)
			TypeCache.Refresh (14ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (403ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (1634ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1410ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (572ms)
			ProcessInitializeOnLoadAttributes (763ms)
			ProcessInitializeOnLoadMethodAttributes (52ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 1.33 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7563 unused Assets / (11.3 MB). Loaded Objects now: 8398.
Memory consumption went from 192.4 MB to 181.1 MB.
Total: 13.495400 ms (FindLiveObjects: 0.840300 ms CreateObjectMapping: 0.607200 ms MarkObjects: 6.745600 ms  DeleteObjects: 5.300500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.414 seconds
Refreshing native plugins compatible for Editor in 0.86 ms, found 4 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.779 seconds
Domain Reload Profiling: 2194ms
	BeginReloadAssembly (196ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (49ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (26ms)
	LoadAllAssembliesAndSetupDomain (1155ms)
		LoadAssemblies (1072ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (181ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (160ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (779ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (602ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (13ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (158ms)
			ProcessInitializeOnLoadAttributes (360ms)
			ProcessInitializeOnLoadMethodAttributes (60ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 1.01 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7563 unused Assets / (10.1 MB). Loaded Objects now: 8400.
Memory consumption went from 192.3 MB to 182.1 MB.
Total: 8.791600 ms (FindLiveObjects: 0.695100 ms CreateObjectMapping: 0.604800 ms MarkObjects: 3.917700 ms  DeleteObjects: 3.573400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.736 seconds
Refreshing native plugins compatible for Editor in 0.98 ms, found 4 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.773 seconds
Domain Reload Profiling: 1511ms
	BeginReloadAssembly (174ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (13ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (44ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (23ms)
	LoadAllAssembliesAndSetupDomain (502ms)
		LoadAssemblies (400ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (184ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (161ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (774ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (595ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (11ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (174ms)
			ProcessInitializeOnLoadAttributes (338ms)
			ProcessInitializeOnLoadMethodAttributes (60ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 0.97 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7563 unused Assets / (10.1 MB). Loaded Objects now: 8402.
Memory consumption went from 192.3 MB to 182.1 MB.
Total: 8.535000 ms (FindLiveObjects: 0.646900 ms CreateObjectMapping: 0.559500 ms MarkObjects: 3.505600 ms  DeleteObjects: 3.822000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 1227.588504 seconds.
  path: Assets/Resources/Prefabs/DamageLabelCoin.prefab
  artifactKey: Guid(31f94c3dc423b0f4ea25fea123c649dd) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/DamageLabelCoin.prefab using Guid(31f94c3dc423b0f4ea25fea123c649dd) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'cb8039ed68786bbc23933803e4f93fa5') in 0.4570322 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 26

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Resources/Prefabs/DamageLabelVenom.prefab
  artifactKey: Guid(134b4d053a5ca5040b4ef69b56cd1ac2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/DamageLabelVenom.prefab using Guid(134b4d053a5ca5040b4ef69b56cd1ac2) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '4845778bdddc1ac541dd6b3ce1a91c0c') in 0.0290993 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 26

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Resources/Prefabs/DamageLabelFire.prefab
  artifactKey: Guid(13b2fd1dc8f0f8d41a31657ff1140c13) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/DamageLabelFire.prefab using Guid(13b2fd1dc8f0f8d41a31657ff1140c13) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '4e5e60b391aa0ca79675918a0bb37cb9') in 0.0271685 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 26

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Resources/Prefabs/DamageLabelStrength.prefab
  artifactKey: Guid(4ce31a05633ad174289e800ea35e02db) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/DamageLabelStrength.prefab using Guid(4ce31a05633ad174289e800ea35e02db) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd29e08067a70c73134d981a096b89339') in 0.0278662 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 26

Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0