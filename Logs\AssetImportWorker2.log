Using pre-set license
Built from '6000.0/staging' branch; Version is '6000.0.36f1 (9fe3b5f71dbb) revision 10478517'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 10  (10.0.19045) 64bit Professional' Language: 'pt' Physical Memory: 16221 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\6000.0.36f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker2
-projectPath
D:/Menino Autista/DKG-RPG-Mobile
-logFile
Logs/AssetImportWorker2.log
-srvPort
65077
-job-worker-count
7
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: D:/Menino Autista/DKG-RPG-Mobile
D:/Menino Autista/DKG-RPG-Mobile
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [23404]  Target information:

Player connection [23404]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 930318080 [EditorId] 930318080 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-J26I52U) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [23404] Host joined multi-casting on [***********:54997]...
Player connection [23404] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 7
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 1.94 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.36f1 (9fe3b5f71dbb)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/Menino Autista/DKG-RPG-Mobile/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce GTX 1050 Ti (ID=0x1c82)
    Vendor:   NVIDIA
    VRAM:     4004 MB
    Driver:   32.0.15.6094
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56888
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.dll
Registered in 0.004252 seconds.
- Loaded All Assemblies, in  0.350 seconds
[usbmuxd] Start listen thread
[usbmuxd] Listen thread started
Native extension for iOS target not found
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 209 ms
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.570 seconds
Domain Reload Profiling: 918ms
	BeginReloadAssembly (117ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (43ms)
	LoadAllAssembliesAndSetupDomain (144ms)
		LoadAssemblies (116ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (141ms)
			TypeCache.Refresh (139ms)
				TypeCache.ScanAssembly (128ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (571ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (524ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (311ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (48ms)
			ProcessInitializeOnLoadAttributes (113ms)
			ProcessInitializeOnLoadMethodAttributes (48ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.896 seconds
Refreshing native plugins compatible for Editor in 0.89 ms, found 4 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.923 seconds
Domain Reload Profiling: 1817ms
	BeginReloadAssembly (151ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (28ms)
	RebuildCommonClasses (34ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (32ms)
	LoadAllAssembliesAndSetupDomain (666ms)
		LoadAssemblies (436ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (318ms)
			TypeCache.Refresh (232ms)
				TypeCache.ScanAssembly (213ms)
			BuildScriptInfoCaches (71ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (923ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (767ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (15ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (190ms)
			ProcessInitializeOnLoadAttributes (452ms)
			ProcessInitializeOnLoadMethodAttributes (98ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (4ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.01 seconds
Refreshing native plugins compatible for Editor in 1.21 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 324 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7564 unused Assets / (10.2 MB). Loaded Objects now: 8362.
Memory consumption went from 238.1 MB to 227.9 MB.
Total: 9.478300 ms (FindLiveObjects: 0.788500 ms CreateObjectMapping: 0.864000 ms MarkObjects: 4.034300 ms  DeleteObjects: 3.790700 ms)

========================================================================
Received Import Request.
  Time since last request: 90907.133118 seconds.
  path: Assets/Resources/Prefabs/DamageLabelFire.prefab
  artifactKey: Guid(13b2fd1dc8f0f8d41a31657ff1140c13) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/DamageLabelFire.prefab using Guid(13b2fd1dc8f0f8d41a31657ff1140c13) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '66c009b67a8c40320f70e287bce88c65') in 0.4520276 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 26

========================================================================
Received Import Request.
  Time since last request: 19.453569 seconds.
  path: Assets/Resources/Scripts/Debug
  artifactKey: Guid(eb145d66c386d414a845e02ac51c49b5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Scripts/Debug using Guid(eb145d66c386d414a845e02ac51c49b5) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '003aef6383a75ab9f4eea3fd86b020ff') in 0.0007256 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 3.829712 seconds.
  path: Assets/Resources/Scripts/Editor
  artifactKey: Guid(b81495eae40f7374baaa870b03171675) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Scripts/Editor using Guid(b81495eae40f7374baaa870b03171675) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '757c09674c7d66ee432cce3d7cfe636a') in 0.0004355 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in 11.791 seconds
Refreshing native plugins compatible for Editor in 1.02 ms, found 4 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.845 seconds
Domain Reload Profiling: 13639ms
	BeginReloadAssembly (883ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (171ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (126ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (24ms)
	LoadAllAssembliesAndSetupDomain (10847ms)
		LoadAssemblies (10791ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (562ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (527ms)
			ResolveRequiredComponents (19ms)
	FinalizeReload (1845ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1580ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (18ms)
			SetLoadedEditorAssemblies (110ms)
			BeforeProcessingInitializeOnLoad (613ms)
			ProcessInitializeOnLoadAttributes (778ms)
			ProcessInitializeOnLoadMethodAttributes (57ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 1.25 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7563 unused Assets / (10.1 MB). Loaded Objects now: 8398.
Memory consumption went from 194.2 MB to 184.1 MB.
Total: 10.720700 ms (FindLiveObjects: 0.822800 ms CreateObjectMapping: 0.614200 ms MarkObjects: 5.190700 ms  DeleteObjects: 4.091000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.390 seconds
Refreshing native plugins compatible for Editor in 1.15 ms, found 4 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.769 seconds
Domain Reload Profiling: 2160ms
	BeginReloadAssembly (197ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (50ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (28ms)
	LoadAllAssembliesAndSetupDomain (1128ms)
		LoadAssemblies (1027ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (197ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (172ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (769ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (585ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (149ms)
			ProcessInitializeOnLoadAttributes (357ms)
			ProcessInitializeOnLoadMethodAttributes (57ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
Refreshing native plugins compatible for Editor in 1.06 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7563 unused Assets / (10.2 MB). Loaded Objects now: 8400.
Memory consumption went from 194.0 MB to 183.8 MB.
Total: 9.583800 ms (FindLiveObjects: 0.679500 ms CreateObjectMapping: 0.694200 ms MarkObjects: 4.276700 ms  DeleteObjects: 3.929700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.754 seconds
Refreshing native plugins compatible for Editor in 0.92 ms, found 4 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.746 seconds
Domain Reload Profiling: 1502ms
	BeginReloadAssembly (176ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (14ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (45ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (23ms)
	LoadAllAssembliesAndSetupDomain (518ms)
		LoadAssemblies (402ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (195ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (175ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (746ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (571ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (151ms)
			ProcessInitializeOnLoadAttributes (339ms)
			ProcessInitializeOnLoadMethodAttributes (59ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 0.96 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7563 unused Assets / (10.0 MB). Loaded Objects now: 8402.
Memory consumption went from 194.0 MB to 184.0 MB.
Total: 8.154200 ms (FindLiveObjects: 0.626200 ms CreateObjectMapping: 0.565000 ms MarkObjects: 3.622000 ms  DeleteObjects: 3.340100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 1204.350964 seconds.
  path: Assets/Resources/Prefabs/DamageLabelAcid.prefab
  artifactKey: Guid(44c10bd0cebf38641a341925ce9ba57d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/DamageLabelAcid.prefab using Guid(44c10bd0cebf38641a341925ce9ba57d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b0500feb727368353796b3e31dcc4a05') in 0.4321142 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 26

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Resources/Prefabs/DamageLabelElectricity.prefab
  artifactKey: Guid(e14da7c344d28694d83f19abab85690e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/DamageLabelElectricity.prefab using Guid(e14da7c344d28694d83f19abab85690e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e8d68d8bc14c776f0937da455588147b') in 0.0277734 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 26

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Resources/Prefabs/DamageLabelMagic.prefab
  artifactKey: Guid(c15b00405f937e04fa7d6a9169b26fa2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/DamageLabelMagic.prefab using Guid(c15b00405f937e04fa7d6a9169b26fa2) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '82875bf804a11f451a0b68c0adc10d21') in 0.0278269 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 26

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Resources/Prefabs/DamageLabelPossession.prefab
  artifactKey: Guid(b46a5b7c4f52fda4fa43f7fae34529be) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/DamageLabelPossession.prefab using Guid(b46a5b7c4f52fda4fa43f7fae34529be) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '89fa4909383a645b71cdf5635d44a8a6') in 0.0288911 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 26

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Resources/Prefabs/DamageLabelFrost.prefab
  artifactKey: Guid(768178223d7e3c84e83395a3a9889e85) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/DamageLabelFrost.prefab using Guid(768178223d7e3c84e83395a3a9889e85) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '341ebe999e7971578c7180b99e7661b7') in 0.0269833 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 26

Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0