using UnityEngine;
using UnityEngine.UI;
using UnityEngine.EventSystems;

public class SwitchConfigs : MonoBehaviour
{
    public GameObject CharButton; // 0
    public GameObject BuffsButton; // 1
    public GameObject PartyButton; // 2

    public GameObject GamePlayConfigs;

    void Start()
    {
        CharButton.GetComponent<Button>().onClick.AddListener(() => SwitchConfs(0));
        BuffsButton.GetComponent<Button>().onClick.AddListener(() => SwitchConfs(1));
        PartyButton.GetComponent<Button>().onClick.AddListener(() => SwitchConfs(2));

        // Prevent event propagation on child elements
        PreventEventPropagationOnChildren(CharButton);
        PreventEventPropagationOnChildren(BuffsButton);
        PreventEventPropagationOnChildren(PartyButton);
    }

    private void SwitchConfs(int conf) // switch configs
    {
        // checks if the current config is active
        bool isActive = transform.GetChild(conf).GetChild(0).gameObject.activeSelf;
        foreach(var child in transform.GetComponentsInChildren<Button>()) if(child.transform.parent == transform) child.transform.GetChild(0).gameObject.SetActive(false);

        // sets the game play configs acording to the current config
        GamePlayConfigs.SetActive(isActive);

        // if the config wasn't active, turns it on
        if(!isActive) transform.GetChild(conf).transform.GetChild(0).gameObject.SetActive(true);
    }

    private void PreventEventPropagationOnChildren(GameObject button)
    {
        // Get all child UI elements that might receive clicks
        var childGraphics = button.GetComponentsInChildren<Graphic>();

        foreach (var graphic in childGraphics)
        {
            // Skip the button itself (parent)
            if (graphic.gameObject == button)
                continue;

            // Add a component that blocks raycasts but doesn't handle clicks
            if (graphic.GetComponent<NonInteractiveGraphic>() == null)
            {
                graphic.gameObject.AddComponent<NonInteractiveGraphic>();
            }
        }
    }
}

// Component to prevent event propagation on child UI elements
public class NonInteractiveGraphic : MonoBehaviour, IPointerClickHandler
{
    public void OnPointerClick(PointerEventData eventData)
    {
        // Consume the click event to prevent it from bubbling up to parent
        // Do nothing - this stops the event propagation
    }
}
