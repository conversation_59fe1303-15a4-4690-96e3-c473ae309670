﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio 15
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Assembly-CSharp", "Assembly-CSharp.csproj", "{AE1A63DF-7F5F-5CC3-7DE4-2FD71B7EE6B4}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "spine-unity-editor", "spine-unity-editor.csproj", "{B5C2F547-782A-F0D4-1E73-AF1C8496BA0C}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "NativeFilePicker.Runtime", "NativeFilePicker.Runtime.csproj", "{30A7BABB-BDBA-7965-E174-355EF3D979F7}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "spine-csharp", "spine-csharp.csproj", "{4C8F4B1F-FF5B-7862-D90C-72C080D53CFF}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "spine-unity", "spine-unity.csproj", "{CBAC324B-7FC3-375D-2A87-10D439DB9798}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Assembly-CSharp-firstpass", "Assembly-CSharp-firstpass.csproj", "{6CAE8D53-6BC9-5CCB-740F-A4959D3EEA87}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "NativeFilePicker.Editor", "NativeFilePicker.Editor.csproj", "{BA137EC1-B3A1-116F-5B77-426D3122087F}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "OutlineFx", "OutlineFx.csproj", "{2F49E5A3-76C4-8885-6B5C-F2A7E0DF931A}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "OutlineFx.Editor", "OutlineFx.Editor.csproj", "{EA487BE3-235B-40BE-8FB6-6BAFC9F029DC}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Assembly-CSharp-Editor", "Assembly-CSharp-Editor.csproj", "{F92FDF6A-91DF-8B2B-F1D6-F0BA9D631D2A}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{AE1A63DF-7F5F-5CC3-7DE4-2FD71B7EE6B4}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{AE1A63DF-7F5F-5CC3-7DE4-2FD71B7EE6B4}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{AE1A63DF-7F5F-5CC3-7DE4-2FD71B7EE6B4}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{AE1A63DF-7F5F-5CC3-7DE4-2FD71B7EE6B4}.Release|Any CPU.Build.0 = Release|Any CPU
		{B5C2F547-782A-F0D4-1E73-AF1C8496BA0C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B5C2F547-782A-F0D4-1E73-AF1C8496BA0C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B5C2F547-782A-F0D4-1E73-AF1C8496BA0C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B5C2F547-782A-F0D4-1E73-AF1C8496BA0C}.Release|Any CPU.Build.0 = Release|Any CPU
		{30A7BABB-BDBA-7965-E174-355EF3D979F7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{30A7BABB-BDBA-7965-E174-355EF3D979F7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{30A7BABB-BDBA-7965-E174-355EF3D979F7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{30A7BABB-BDBA-7965-E174-355EF3D979F7}.Release|Any CPU.Build.0 = Release|Any CPU
		{4C8F4B1F-FF5B-7862-D90C-72C080D53CFF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4C8F4B1F-FF5B-7862-D90C-72C080D53CFF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4C8F4B1F-FF5B-7862-D90C-72C080D53CFF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4C8F4B1F-FF5B-7862-D90C-72C080D53CFF}.Release|Any CPU.Build.0 = Release|Any CPU
		{CBAC324B-7FC3-375D-2A87-10D439DB9798}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{CBAC324B-7FC3-375D-2A87-10D439DB9798}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{CBAC324B-7FC3-375D-2A87-10D439DB9798}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{CBAC324B-7FC3-375D-2A87-10D439DB9798}.Release|Any CPU.Build.0 = Release|Any CPU
		{6CAE8D53-6BC9-5CCB-740F-A4959D3EEA87}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6CAE8D53-6BC9-5CCB-740F-A4959D3EEA87}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6CAE8D53-6BC9-5CCB-740F-A4959D3EEA87}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6CAE8D53-6BC9-5CCB-740F-A4959D3EEA87}.Release|Any CPU.Build.0 = Release|Any CPU
		{BA137EC1-B3A1-116F-5B77-426D3122087F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{BA137EC1-B3A1-116F-5B77-426D3122087F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{BA137EC1-B3A1-116F-5B77-426D3122087F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{BA137EC1-B3A1-116F-5B77-426D3122087F}.Release|Any CPU.Build.0 = Release|Any CPU
		{2F49E5A3-76C4-8885-6B5C-F2A7E0DF931A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2F49E5A3-76C4-8885-6B5C-F2A7E0DF931A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2F49E5A3-76C4-8885-6B5C-F2A7E0DF931A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2F49E5A3-76C4-8885-6B5C-F2A7E0DF931A}.Release|Any CPU.Build.0 = Release|Any CPU
		{EA487BE3-235B-40BE-8FB6-6BAFC9F029DC}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{EA487BE3-235B-40BE-8FB6-6BAFC9F029DC}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{EA487BE3-235B-40BE-8FB6-6BAFC9F029DC}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{EA487BE3-235B-40BE-8FB6-6BAFC9F029DC}.Release|Any CPU.Build.0 = Release|Any CPU
		{F92FDF6A-91DF-8B2B-F1D6-F0BA9D631D2A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F92FDF6A-91DF-8B2B-F1D6-F0BA9D631D2A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F92FDF6A-91DF-8B2B-F1D6-F0BA9D631D2A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F92FDF6A-91DF-8B2B-F1D6-F0BA9D631D2A}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
EndGlobal
